import bisect

def find_longest_increasing_subsequence(sequence):
    tails = []

    for number in sequence:
        if not tails or number > tails[-1]:
            tails.append(number)

        else:
            # Find the smallest tail that is >= number.
            left, right = 0, len(tails) - 1

            while left <= right:
                middle = (left + right) // 2

                if tails[middle] < number:
                    left = middle + 1
                else:
                    right = middle - 1

            # Replace that found tail.
            tails[left] = number
        print(f"Processed number: {number}, Tails: {tails}")

    # Length of tails is our LIS length.
    return len(tails)

print(find_longest_increasing_subsequence([10, 9, 2, 5, 3, 7, 101, 18]))

def longest_increasing_subsequence(nums):
    """
    Find the length of the longest increasing subsequence.

    Time Complexity: O(n log n)
    Space Complexity: O(n)

    Algorithm: Binary Search + Patience Sorting
    - Maintain 'tails' array where tails[i] is the smallest ending element
      of all increasing subsequences of length i+1
    - For each number, use binary search to find its position in tails
    - Either extend the sequence or replace an element for better future options
    """
    if not nums:
        return 0

    # tails[i] = smallest ending element of all increasing subsequences of length i+1
    tails = []

    for num in nums:
        # Binary search to find the position where num should be placed
        # bisect_left returns the leftmost position where num can be inserted
        # to keep the array sorted
        pos = bisect.bisect_left(tails, num)

        if pos == len(tails):
            # num is larger than all elements in tails
            # This extends our longest subsequence
            tails.append(num)
        else:
            # Replace tails[pos] with num
            # This gives us a better (smaller) ending element for subsequences of length pos+1
            tails[pos] = num

    # The length of tails is the length of the LIS
    return len(tails)

def longest_increasing_subsequence_with_sequence(nums):
    """
    Return both the length and the actual longest increasing subsequence.

    Time Complexity: O(n log n)
    Space Complexity: O(n)
    """
    if not nums:
        return 0, []

    tails = []
    # Store the actual elements and their positions for reconstruction
    predecessors = [-1] * len(nums)  # To track the previous element in LIS
    tail_indices = []  # Store indices corresponding to tails

    for i, num in enumerate(nums):
        pos = bisect.bisect_left(tails, num)

        if pos == len(tails):
            tails.append(num)
            tail_indices.append(i)
        else:
            tails[pos] = num
            tail_indices[pos] = i

        # Set predecessor for reconstruction
        if pos > 0:
            predecessors[i] = tail_indices[pos - 1]

    # Reconstruct the actual LIS
    lis_length = len(tails)
    if lis_length == 0:
        return 0, []

    # Start from the last element and trace back
    lis = []
    current = tail_indices[-1]

    while current != -1:
        lis.append(nums[current])
        current = predecessors[current]

    lis.reverse()
    return lis_length, lis

# Test with the original example
print(longest_increasing_subsequence([10, 9, 2, 5, 3, 7, 101, 18]))