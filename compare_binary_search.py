# Both implementations from the original file
def binary_search_recursive(nums, target, start, end):
    """Recursive binary search from the original file."""
    if start > end:
        return -1
    mid = (start + end) // 2
    if target == nums[mid]:
        return mid
    elif target < nums[mid]:
        return binary_search_recursive(nums, target, start, mid - 1)
    else:
        return binary_search_recursive(nums, target, mid + 1, end)

def binary_search_iterative(nums, target):
    """Iterative binary search from the original file."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target == nums[mid]:
            return mid
        elif target < nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    return -1

def compare_implementations():
    """Compare recursive vs iterative implementations."""
    
    test_cases = [
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 5),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 1),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 9),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 10),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 0),
        ([5], 5),
        ([5], 3),
        ([], 1),
        ([1, 3, 5, 7, 9, 11], 7),
        ([2, 4, 6, 8, 10], 6),
        ([1, 2, 2, 2, 3], 2),  # Duplicates
    ]
    
    print("COMPARING RECURSIVE VS ITERATIVE BINARY SEARCH")
    print("=" * 60)
    
    all_match = True
    
    for i, (nums, target) in enumerate(test_cases, 1):
        recursive_result = binary_search_recursive(nums, target, 0, len(nums) - 1)
        iterative_result = binary_search_iterative(nums, target)
        
        match = recursive_result == iterative_result
        all_match = all_match and match
        
        status = "✅ MATCH" if match else "❌ DIFFER"
        print(f"Test {i:2d}: {status}")
        print(f"         Array: {nums}")
        print(f"         Target: {target}")
        print(f"         Recursive: {recursive_result}")
        print(f"         Iterative: {iterative_result}")
        
        if not match:
            print(f"         ❌ IMPLEMENTATIONS DIFFER!")
        
        print()
    
    print(f"OVERALL: {'✅ ALL IMPLEMENTATIONS MATCH' if all_match else '❌ IMPLEMENTATIONS DIFFER'}")
    return all_match

def analyze_complexity():
    """Analyze time and space complexity."""
    
    print("\nCOMPLEXITY ANALYSIS")
    print("=" * 30)
    
    print("ITERATIVE BINARY SEARCH:")
    print("  Time Complexity:  O(log n)")
    print("  Space Complexity: O(1) - constant space")
    print("  Pros: No function call overhead, no stack overflow risk")
    print("  Cons: Slightly more verbose loop logic")
    
    print("\nRECURSIVE BINARY SEARCH:")
    print("  Time Complexity:  O(log n)")
    print("  Space Complexity: O(log n) - due to call stack")
    print("  Pros: More elegant, easier to understand")
    print("  Cons: Function call overhead, potential stack overflow")

if __name__ == "__main__":
    compare_implementations()
    analyze_complexity()
