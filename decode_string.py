def decode_string(encoded_string):
    string_stack = []
    current_string = ''
    current_number = 0

    for char in encoded_string:
        if char.isdigit():
            current_number = current_number * 10 + int(char)
        elif char == '[':
            # Save current state before diving into substring
            string_stack.append((current_string, current_number))
            current_string = ''
            current_number = 0

        elif char == ']':
            # Retrieve previous state to build decoded string
            previous_string, repeat_count = string_stack.pop()
            current_string = previous_string + current_string * repeat_count

        else:
            # Append chars to current substring
            current_string += char
        print(f"Processing char: {char}, stack: {string_stack}, current_string: {current_string}, current_number: {current_number}")

    return current_string

# print(decode_string("3[a]2[bc]"))
print(decode_string("3[a2[c]]"))
# print(decode_string("2[abc]3[cd]ef")