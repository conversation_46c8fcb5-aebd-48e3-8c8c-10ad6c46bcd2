def longest_palindromic_substring(s):
    """
    Find the longest palindromic substring using expand around centers approach.
    Time Complexity: O(n²)
    Space Complexity: O(1)
    """
    if not s:
        return ""

    start = 0
    max_length = 1

    def expand_around_center(left, right):
        """Helper function to expand around center and return length of palindrome."""
        while left >= 0 and right < len(s) and s[left] == s[right]:
            left -= 1
            right += 1
        return right - left - 1

    for i in range(len(s)):
        # Check for odd-length palindromes (center at i)
        length1 = expand_around_center(i, i)

        # Check for even-length palindromes (center between i and i+1)
        length2 = expand_around_center(i, i + 1)

        # Get the maximum length palindrome centered at i
        current_max = max(length1, length2)

        if current_max > max_length:
            max_length = current_max
            # Calculate start position based on center and length
            start = i - (current_max - 1) // 2
            print(f"New longest palindrome found: {s[start:start + max_length]} with length {max_length} centered at {i}")

    return s[start:start + max_length]

print(longest_palindromic_substring("cabad"))
# print(longest_palindromic_substring("cbbd"))
