
import heapq
from collections import Counter

def top_k_frequent_elements_optimal(nums, k):
    # Step 1: Count frequencies - O(n)
    freq_count = Counter(nums)
    
    # Step 2: Use min-heap to maintain top k - O(n log k)
    heap = []
    for num, freq in freq_count.items():
        heapq.heappush(heap, (freq, num))
        if len(heap) > k:
            heapq.heappop(heap)
    
    # Step 3: Extract results - O(k)
    return [num for _, num in heap]


print(top_k_frequent_elements_optimal([1, 1, 1, 2, 2, 3], 2))
# print(top_k_frequent_elements_optimal([1], 1))
