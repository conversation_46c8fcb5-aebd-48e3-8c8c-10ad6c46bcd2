def find_first_one(nums):
    left, right = 0, len(nums) - 1
    result = -1
    while left <= right:
        mid = (left + right) // 2
        if nums[mid] == 1:
            result = mid
            right = mid - 1
        else:
            left = mid + 1
    return result

def lower_bound(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if nums[mid] >= target:
            right = mid - 1
        else:
            left = mid + 1
    return left

def square_root(number):
    left, right = 0, number
    while left <= right:
        mid = (left + right) // 2
        print(f"left={left}, right={right}, mid={mid}, mid*mid={mid*mid}")
        if mid * mid <= number:
            left = mid + 1
        else:
            right = mid - 1
    return right

# print(find_first_one([0, 0, 0, 1, 1, 1, 1]))
# print(lower_bound([1, 2, 3, 5, 6, 7, 8], 9))
print(square_root(100))