"""
Debug Analysis for first_position_of_element_in_sorted_array
"""

def first_position_buggy(nums, target, left, right):
    """
    BUGGY VERSION - Let's trace through what's wrong
    """
    print(f"🔍 Searching in range [{left}, {right}], target={target}")
    
    if left > right:
        print(f"❌ Base case: left > right, returning -1")
        return -1
    
    pos = -1
    mid = (left + right) // 2
    print(f"📍 mid={mid}, nums[mid]={nums[mid]}")
    
    if target == nums[mid]:
        print(f"✅ Found target at mid={mid}")
        pos = mid
    
    # BUG: This logic is flawed!
    if target <= nums[mid]:
        print(f"🔄 target <= nums[mid], searching left half")
        pos = first_position_buggy(nums, target, left, mid - 1)
    else:
        print(f"🔄 target > nums[mid], searching right half")
        pos = first_position_buggy(nums, target, mid + 1, right)
    
    print(f"🔙 Returning pos={pos}")
    return pos

def first_position_fixed(nums, target, left, right):
    """
    FIXED VERSION - Correct logic for finding first position
    """
    print(f"🔍 Searching in range [{left}, {right}], target={target}")
    
    if left > right:
        print(f"❌ Base case: left > right, returning -1")
        return -1
    
    mid = (left + right) // 2
    print(f"📍 mid={mid}, nums[mid]={nums[mid]}")
    
    if nums[mid] == target:
        print(f"✅ Found target at mid={mid}")
        # Check if this is the first occurrence
        if mid == 0 or nums[mid - 1] != target:
            print(f"🎯 This is the FIRST occurrence!")
            return mid
        else:
            print(f"🔄 Not first occurrence, search left for earlier one")
            return first_position_fixed(nums, target, left, mid - 1)
    elif nums[mid] < target:
        print(f"🔄 nums[mid] < target, search right half")
        return first_position_fixed(nums, target, mid + 1, right)
    else:  # nums[mid] > target
        print(f"🔄 nums[mid] > target, search left half")
        return first_position_fixed(nums, target, left, mid - 1)

def first_position_iterative(nums, target):
    """
    ITERATIVE VERSION - More efficient, no recursion
    """
    left, right = 0, len(nums) - 1
    result = -1
    
    print(f"🔍 Iterative search for target={target}")
    
    while left <= right:
        mid = (left + right) // 2
        print(f"📍 Range [{left}, {right}], mid={mid}, nums[mid]={nums[mid]}")
        
        if nums[mid] == target:
            print(f"✅ Found target at mid={mid}")
            result = mid  # Store potential result
            right = mid - 1  # Continue searching left for first occurrence
            print(f"🔄 Continue searching left for earlier occurrence")
        elif nums[mid] < target:
            print(f"🔄 nums[mid] < target, search right")
            left = mid + 1
        else:
            print(f"🔄 nums[mid] > target, search left")
            right = mid - 1
    
    print(f"🎯 Final result: {result}")
    return result

def analyze_bug():
    """
    Analyze what's wrong with the original implementation
    """
    print("BUG ANALYSIS")
    print("=" * 40)
    print("""
PROBLEM WITH ORIGINAL CODE:

def first_position_of_element_in_sorted_array(nums, target, left, right):
    # ... base case ...
    pos = -1
    mid = (left + right) // 2
    
    if target == nums[mid]:
        pos = mid                    # ✅ Found target
    
    if target <= nums[mid]:          # 🐛 BUG HERE!
        pos = first_position_buggy(nums, target, left, mid - 1)
    else:
        pos = first_position_buggy(nums, target, mid + 1, right)
    
    return pos

ISSUES:
1. 🐛 LOGIC ERROR: When target == nums[mid], it sets pos = mid, 
   but then ALWAYS overwrites it by searching left (target <= nums[mid])

2. 🐛 OVERWRITES RESULT: Even when we find the target, we overwrite 
   the result with the recursive call

3. 🐛 WRONG CONDITION: Should check if current position is the FIRST 
   occurrence, not just search left unconditionally

4. 🐛 NO EARLY TERMINATION: Doesn't stop when finding the first occurrence

TRACE FOR nums=[5,7,7,8,8,10], target=8:
- mid=2, nums[2]=7, target > nums[mid] → search right
- mid=4, nums[4]=8, target == nums[mid] → pos=4
- BUT then target <= nums[mid] → search left, overwrites pos!
- mid=3, nums[3]=8, target == nums[mid] → pos=3  
- BUT then target <= nums[mid] → search left again!
- Eventually returns -1 because it keeps searching left past valid range
""")

def test_all_versions():
    """
    Test all versions with various test cases
    """
    print("\nTEST CASES")
    print("=" * 20)
    
    test_cases = [
        ([5, 7, 7, 8, 8, 10], 8, "Original example"),
        ([1, 2, 3, 4, 5], 3, "Single occurrence"),
        ([1, 1, 1, 1, 1], 1, "All same elements"),
        ([1, 2, 3, 4, 5], 6, "Target not found"),
        ([2, 2, 2, 2, 2], 2, "All same, target exists"),
        ([], 1, "Empty array"),
        ([5], 5, "Single element, found"),
        ([5], 3, "Single element, not found"),
    ]
    
    for nums, target, description in test_cases:
        print(f"\n📋 Test: {description}")
        print(f"   Array: {nums}, Target: {target}")
        
        if nums:  # Only test recursive version if array is not empty
            print("   🐛 Buggy version:")
            try:
                result_buggy = first_position_buggy(nums, target, 0, len(nums) - 1)
                print(f"   Result: {result_buggy}")
            except RecursionError:
                print("   Result: RecursionError (infinite recursion)")
            
            print("   ✅ Fixed recursive version:")
            result_fixed = first_position_fixed(nums, target, 0, len(nums) - 1)
            print(f"   Result: {result_fixed}")
        
        print("   🚀 Iterative version:")
        result_iterative = first_position_iterative(nums, target)
        print(f"   Result: {result_iterative}")
        
        # Verify with Python's built-in
        try:
            expected = nums.index(target)
        except ValueError:
            expected = -1
        
        print(f"   ✅ Expected: {expected}")

def corrected_implementation():
    """
    Show the corrected implementation
    """
    print("\nCORRECTED IMPLEMENTATION")
    print("=" * 30)
    print("""
def first_position_of_element_in_sorted_array(nums, target, left, right):
    if left > right:
        return -1
    
    mid = (left + right) // 2
    
    if nums[mid] == target:
        # Check if this is the first occurrence
        if mid == 0 or nums[mid - 1] != target:
            return mid  # This is the first occurrence
        else:
            # Search left for an earlier occurrence
            return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)
    elif nums[mid] < target:
        # Target is in the right half
        return first_position_of_element_in_sorted_array(nums, target, mid + 1, right)
    else:
        # Target is in the left half
        return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)

KEY FIXES:
1. ✅ Proper condition checking for first occurrence
2. ✅ No overwriting of found results
3. ✅ Correct recursive calls based on comparison
4. ✅ Early termination when first occurrence is found
""")

if __name__ == "__main__":
    analyze_bug()
    test_all_versions()
    corrected_implementation()
