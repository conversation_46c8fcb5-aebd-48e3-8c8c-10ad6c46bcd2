"""
Comprehensive test suite for the fixed first_position_of_element_in_sorted_array
"""

def first_position_of_element_in_sorted_array(nums, target, left, right):
    """
    Fixed version - Find the first position of target in a sorted array.
    """
    if left > right:
        return -1
    
    mid = (left + right) // 2
    
    if nums[mid] == target:
        # Check if this is the first occurrence
        if mid == 0 or nums[mid - 1] != target:
            return mid  # This is the first occurrence
        else:
            # Search left for an earlier occurrence
            return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)
    elif nums[mid] < target:
        # Target is in the right half
        return first_position_of_element_in_sorted_array(nums, target, mid + 1, right)
    else:
        # Target is in the left half
        return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)

def first_position_iterative(nums, target):
    """
    Iterative version for comparison and efficiency.
    """
    left, right = 0, len(nums) - 1
    result = -1
    
    while left <= right:
        mid = (left + right) // 2
        
        if nums[mid] == target:
            result = mid  # Store potential result
            right = mid - 1  # Continue searching left for first occurrence
        elif nums[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    
    return result

def run_comprehensive_tests():
    """
    Run comprehensive tests to verify the fix works correctly.
    """
    print("COMPREHENSIVE TEST SUITE FOR FIXED FIRST POSITION")
    print("=" * 55)
    
    test_cases = [
        # (array, target, expected_result, description)
        ([5, 7, 7, 8, 8, 10], 8, 3, "Original failing case"),
        ([5, 7, 7, 8, 8, 10], 7, 1, "First of two 7s"),
        ([5, 7, 7, 8, 8, 10], 5, 0, "First element"),
        ([5, 7, 7, 8, 8, 10], 10, 5, "Last element"),
        ([5, 7, 7, 8, 8, 10], 6, -1, "Not found between elements"),
        ([5, 7, 7, 8, 8, 10], 1, -1, "Not found, smaller than all"),
        ([5, 7, 7, 8, 8, 10], 15, -1, "Not found, larger than all"),
        
        ([1, 2, 3, 4, 5], 3, 2, "Single occurrence in middle"),
        ([1, 2, 3, 4, 5], 1, 0, "Single occurrence at start"),
        ([1, 2, 3, 4, 5], 5, 4, "Single occurrence at end"),
        
        ([1, 1, 1, 1, 1], 1, 0, "All same elements"),
        ([2, 2, 2, 2, 2], 2, 0, "All same elements (different value)"),
        
        ([1], 1, 0, "Single element found"),
        ([1], 2, -1, "Single element not found"),
        ([], 1, -1, "Empty array"),
        
        ([1, 1, 2, 2, 3, 3], 1, 0, "Multiple duplicates - first pair"),
        ([1, 1, 2, 2, 3, 3], 2, 2, "Multiple duplicates - middle pair"),
        ([1, 1, 2, 2, 3, 3], 3, 4, "Multiple duplicates - last pair"),
        
        ([1, 2, 2, 2, 2, 3], 2, 1, "Many consecutive duplicates"),
        ([1, 1, 1, 1, 1, 2], 1, 0, "Many consecutive at start"),
        ([1, 2, 2, 2, 2, 2], 2, 1, "Many consecutive at end"),
    ]
    
    print(f"{'Test Case':<35} {'Expected':<10} {'Recursive':<12} {'Iterative':<12} {'Status'}")
    print("-" * 80)
    
    all_passed = True
    
    for nums, target, expected, description in test_cases:
        # Test recursive version
        if nums:  # Only test if array is not empty
            result_recursive = first_position_of_element_in_sorted_array(nums, target, 0, len(nums) - 1)
        else:
            result_recursive = -1
        
        # Test iterative version
        result_iterative = first_position_iterative(nums, target)
        
        # Check if results match expected
        recursive_correct = result_recursive == expected
        iterative_correct = result_iterative == expected
        both_match = result_recursive == result_iterative
        
        if recursive_correct and iterative_correct and both_match:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
            all_passed = False
        
        print(f"{description:<35} {expected:<10} {result_recursive:<12} {result_iterative:<12} {status}")
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The bug has been successfully fixed.")
    else:
        print("❌ Some tests failed. There may still be issues.")
    
    return all_passed

def demonstrate_bug_fix():
    """
    Demonstrate the specific bug that was fixed.
    """
    print("\nBUG FIX DEMONSTRATION")
    print("=" * 25)
    
    nums = [5, 7, 7, 8, 8, 10]
    target = 8
    
    print(f"Array: {nums}")
    print(f"Target: {target}")
    print(f"Expected first position: 3 (nums[3] = 8)")
    print()
    
    print("BEFORE FIX (Original buggy logic):")
    print("- Found target at index 4")
    print("- But then overwrote result by searching left unconditionally")
    print("- Eventually returned -1 due to logic error")
    print()
    
    print("AFTER FIX (Corrected logic):")
    result = first_position_of_element_in_sorted_array(nums, target, 0, len(nums) - 1)
    print(f"- Correctly returns: {result}")
    print("- Properly checks if current position is first occurrence")
    print("- Only searches left when necessary")

def performance_comparison():
    """
    Compare performance of recursive vs iterative approaches.
    """
    import time
    import random
    
    print("\nPERFORMANCE COMPARISON")
    print("=" * 25)
    
    sizes = [100, 1000, 5000, 10000]
    
    print(f"{'Array Size':<12} {'Recursive (ms)':<15} {'Iterative (ms)':<15} {'Speedup'}")
    print("-" * 60)
    
    for size in sizes:
        # Create test array with duplicates
        nums = sorted([random.randint(1, size//10) for _ in range(size)])
        target = nums[size//4]  # Pick a target that exists
        
        # Test recursive version
        start_time = time.time()
        for _ in range(100):  # Run multiple times for better measurement
            first_position_of_element_in_sorted_array(nums, target, 0, len(nums) - 1)
        recursive_time = (time.time() - start_time) * 1000 / 100
        
        # Test iterative version
        start_time = time.time()
        for _ in range(100):
            first_position_iterative(nums, target)
        iterative_time = (time.time() - start_time) * 1000 / 100
        
        speedup = recursive_time / iterative_time if iterative_time > 0 else float('inf')
        
        print(f"{size:<12} {recursive_time:<15.3f} {iterative_time:<15.3f} {speedup:.1f}x")

def edge_case_analysis():
    """
    Analyze edge cases and boundary conditions.
    """
    print("\nEDGE CASE ANALYSIS")
    print("=" * 20)
    
    print("✅ EDGE CASES HANDLED:")
    print("1. Empty array → Returns -1")
    print("2. Single element (found/not found) → Correct result")
    print("3. All elements are the same → Returns 0 (first index)")
    print("4. Target at beginning/end → Correct boundary handling")
    print("5. Target not in array → Returns -1")
    print("6. Multiple consecutive duplicates → Returns leftmost index")
    print()
    
    print("🔧 KEY FIXES IMPLEMENTED:")
    print("1. Proper condition: Check if mid is first occurrence")
    print("2. No result overwriting: Return immediately when first found")
    print("3. Correct recursion: Only search left when not first occurrence")
    print("4. Early termination: Stop when first occurrence is confirmed")

if __name__ == "__main__":
    success = run_comprehensive_tests()
    demonstrate_bug_fix()
    performance_comparison()
    edge_case_analysis()
