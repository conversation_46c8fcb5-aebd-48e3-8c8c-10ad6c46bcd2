"""
O(n log n) Longest Increasing Subsequence - Detailed Explanation
"""

import bisect
import time
import random

def lis_dp_original(nums):
    """Original O(n²) DP approach for comparison."""
    if not nums:
        return 0
    dp = [1] * len(nums)
    for i in range(1, len(nums)):
        for j in range(i):
            if nums[i] > nums[j]:
                dp[i] = max(dp[i], dp[j] + 1)
    return max(dp)

def lis_binary_search(nums):
    """O(n log n) binary search approach."""
    if not nums:
        return 0
    
    tails = []
    
    for num in nums:
        pos = bisect.bisect_left(tails, num)
        
        if pos == len(tails):
            tails.append(num)
        else:
            tails[pos] = num
    
    return len(tails)

def step_by_step_example():
    """Show step-by-step execution of the O(n log n) algorithm."""
    
    print("STEP-BY-STEP EXECUTION OF O(n log n) ALGORITHM")
    print("=" * 55)
    
    nums = [10, 9, 2, 5, 3, 7, 101, 18]
    print(f"Input: {nums}")
    print()
    
    tails = []
    
    for i, num in enumerate(nums):
        print(f"Step {i+1}: Processing {num}")
        print(f"  Current tails: {tails}")
        
        # Find position using binary search
        pos = bisect.bisect_left(tails, num)
        print(f"  Binary search position for {num}: {pos}")
        
        if pos == len(tails):
            tails.append(num)
            print(f"  → Extend: tails = {tails}")
        else:
            old_val = tails[pos]
            tails[pos] = num
            print(f"  → Replace tails[{pos}] = {old_val} with {num}: tails = {tails}")
        
        print(f"  Current LIS length: {len(tails)}")
        print()
    
    print(f"Final result: LIS length = {len(tails)}")
    print(f"Final tails array: {tails}")
    print()
    print("Note: The tails array doesn't represent the actual LIS,")
    print("but its length equals the LIS length!")

def algorithm_intuition():
    """Explain the intuition behind the algorithm."""
    
    print("ALGORITHM INTUITION")
    print("=" * 25)
    print("""
KEY INSIGHT: 
For subsequences of the same length, we prefer the one with the smaller ending element
because it gives us more opportunities to extend the subsequence in the future.

WHAT DOES tails[i] REPRESENT?
tails[i] = smallest possible ending element of all increasing subsequences of length i+1

WHY BINARY SEARCH?
- tails array is always sorted (by construction)
- We want to find where the current number fits
- If it's larger than all elements → extend the sequence
- Otherwise → replace an element to get a better ending for that length

EXAMPLE:
If we have subsequences of length 3:
- [1, 5, 8] and [1, 3, 6]
- We prefer to remember ending element 6 (smaller)
- This gives us better chances to extend to length 4

THE MAGIC:
- We don't track all possible subsequences (that would be exponential)
- We only track the "best" ending element for each possible length
- This reduces the problem from O(n²) to O(n log n)
""")

def complexity_analysis():
    """Analyze the complexity of the O(n log n) solution."""
    
    print("COMPLEXITY ANALYSIS")
    print("=" * 25)
    print("""
TIME COMPLEXITY: O(n log n)
- Outer loop: O(n) - iterate through each element
- Binary search: O(log n) - bisect.bisect_left on sorted array
- Total: O(n) × O(log n) = O(n log n)

SPACE COMPLEXITY: O(n)
- tails array: O(k) where k = LIS length ≤ n
- In worst case (entire array is increasing): O(n)
- In best case (no increasing subsequence > 1): O(1)

COMPARISON WITH O(n²) APPROACH:
                    O(n²) DP    O(n log n) Binary Search
Time Complexity:    O(n²)       O(n log n)
Space Complexity:   O(n)        O(n)
Implementation:     Simple      Moderate
Actual LIS:         Easy        Requires extra work
""")

def performance_comparison():
    """Compare performance of both approaches."""
    
    print("PERFORMANCE COMPARISON")
    print("=" * 30)
    
    test_sizes = [100, 500, 1000, 2000, 5000]
    
    print(f"{'Size':<8} {'O(n²) Time':<12} {'O(n log n) Time':<16} {'Speedup':<10} {'Results Match'}")
    print("-" * 65)
    
    for size in test_sizes:
        # Generate random test data
        nums = [random.randint(1, 1000) for _ in range(size)]
        
        # Test O(n²) approach
        start_time = time.time()
        result_dp = lis_dp_original(nums)
        time_dp = time.time() - start_time
        
        # Test O(n log n) approach
        start_time = time.time()
        result_bs = lis_binary_search(nums)
        time_bs = time.time() - start_time
        
        speedup = time_dp / time_bs if time_bs > 0 else float('inf')
        match = result_dp == result_bs
        
        print(f"{size:<8} {time_dp:<12.4f} {time_bs:<16.4f} {speedup:<10.1f}x {match}")

def edge_cases_test():
    """Test edge cases for the O(n log n) implementation."""
    
    print("\nEDGE CASES TEST")
    print("=" * 20)
    
    test_cases = [
        ([], "Empty array"),
        ([1], "Single element"),
        ([1, 2, 3, 4, 5], "Already sorted"),
        ([5, 4, 3, 2, 1], "Reverse sorted"),
        ([1, 1, 1, 1], "All same elements"),
        ([1, 3, 2, 4], "Mixed order"),
        ([10, 9, 2, 5, 3, 7, 101, 18], "Original example"),
    ]
    
    print(f"{'Test Case':<25} {'Input':<20} {'O(n²)':<8} {'O(n log n)':<12} {'Match'}")
    print("-" * 75)
    
    for nums, description in test_cases:
        result_dp = lis_dp_original(nums)
        result_bs = lis_binary_search(nums)
        match = "✅" if result_dp == result_bs else "❌"
        
        nums_str = str(nums) if len(nums) <= 8 else f"{nums[:3]}...+{len(nums)-3}"
        print(f"{description:<25} {nums_str:<20} {result_dp:<8} {result_bs:<12} {match}")

def when_to_use_nlogn():
    """Guidelines for when to use the O(n log n) approach."""
    
    print("\nWHEN TO USE O(n log n) APPROACH")
    print("=" * 35)
    print("""
DEFINITELY USE O(n log n) WHEN:
✅ Input size > 1,000 elements
✅ Performance is critical
✅ Processing multiple large arrays
✅ Real-time applications
✅ Competitive programming
✅ Production systems

CONSIDER O(n²) WHEN:
✅ Input size < 500 elements
✅ Code simplicity is more important than performance
✅ You need to easily modify the algorithm
✅ Educational purposes
✅ You need the actual subsequence (not just length)

HYBRID APPROACH:
def longest_increasing_subsequence(nums):
    if len(nums) < 500:
        return lis_dp_original(nums)  # Simple for small inputs
    else:
        return lis_binary_search(nums)  # Fast for large inputs
""")

if __name__ == "__main__":
    step_by_step_example()
    print("\n" + "="*70 + "\n")
    algorithm_intuition()
    print("\n" + "="*70 + "\n")
    complexity_analysis()
    print("\n" + "="*70 + "\n")
    performance_comparison()
    edge_cases_test()
    when_to_use_nlogn()
