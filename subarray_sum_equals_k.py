from collections import defaultdict

def subarray_sum_equals_k(nums, k):
    """
    Find the number of continuous subarrays whose sum equals to k.

    Args:
        nums (List[int]): List of integers
        k (int): Target sum

    Returns:
        int: Number of continuous subarrays whose sum equals to k
    """
    count = 0
    current_sum = 0
    sum_count = defaultdict(int)
    sum_count[0] = 1  # Initialize with sum 0 having one occurrence

    for num in nums:
        current_sum += num
        if current_sum - k in sum_count:
            count += sum_count[current_sum - k]
        sum_count[current_sum] += 1
        print(f"Current sum: {current_sum}, Count: {count}, Sum count: {sum_count}")

    return count

print(subarray_sum_equals_k([1, 1, 1], 2))
