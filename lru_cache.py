class Node:
    """
    Node for doubly linked list.
    Each node stores key-value pair and pointers to previous/next nodes.
    """
    def __init__(self, key=0, value=0):
        self.key = key
        self.value = value
        self.prev = None
        self.next = None


class LRUCache:
    """
    LRU Cache with true O(1) get and put operations using doubly linked list + HashMap.

    Uses a combination of:
    - HashMap (dict): key -> node mapping for O(1) access
    - Doubly Linked List: maintains order from MRU to LRU

    List structure:
    head <-> node1 <-> node2 <-> ... <-> nodeN <-> tail
    ^                                              ^
    dummy                                        dummy
    (MRU side)                                 (LRU side)

    All operations are truly O(1):
    - get(): O(1) - hash lookup + O(1) list operations
    - put(): O(1) - hash operations + O(1) list operations
    """

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = {}  # key -> node mapping

        # Create dummy head and tail nodes to simplify edge cases
        self.head = Node()  # Dummy head (most recently used side)
        self.tail = Node()  # Dummy tail (least recently used side)

        # Initialize empty list: head <-> tail
        self.head.next = self.tail
        self.tail.prev = self.head

    def _add_node(self, node):
        """Add node right after head (most recently used position). O(1)"""
        node.prev = self.head
        node.next = self.head.next
        self.head.next.prev = node
        self.head.next = node

    def _remove_node(self, node):
        """Remove an existing node from the linked list. O(1)"""
        prev_node = node.prev
        next_node = node.next
        prev_node.next = next_node
        next_node.prev = prev_node

    def _move_to_head(self, node):
        """Move existing node to head (mark as most recently used). O(1)"""
        self._remove_node(node)
        self._add_node(node)

    def _pop_tail(self):
        """Remove the last node (least recently used). O(1)"""
        lru_node = self.tail.prev
        self._remove_node(lru_node)
        return lru_node

    def get(self, key: int) -> int:
        """
        Get value by key and mark as most recently used.

        Time Complexity: O(1)
        - O(1) hash table lookup
        - O(1) move to head operation
        """
        node = self.cache.get(key)

        if not node:
            return -1

        # Move the accessed node to head (most recently used)
        self._move_to_head(node)
        return node.value

    def put(self, key: int, value: int) -> None:
        """
        Insert or update key-value pair.

        Time Complexity: O(1)
        - O(1) hash table operations
        - O(1) linked list operations
        """
        node = self.cache.get(key)

        if not node:
            # Key doesn't exist - add new node
            new_node = Node(key, value)

            # Check if we need to evict LRU item
            if len(self.cache) >= self.capacity:
                # Remove least recently used node
                lru_node = self._pop_tail()
                del self.cache[lru_node.key]

            # Add new node to cache and head of list
            self.cache[key] = new_node
            self._add_node(new_node)

        else:
            # Key exists - update value and move to head
            node.value = value
            self._move_to_head(node)


# Alternative O(1) implementation using OrderedDict (simpler but less educational)
from collections import OrderedDict

class LRUCacheOrderedDict:
    """
    Alternative O(1) LRU Cache using OrderedDict.

    OrderedDict maintains insertion order and provides:
    - O(1) access, insertion, and deletion
    - O(1) move_to_end() for reordering

    This is simpler than the doubly linked list approach but less educational
    since it hides the underlying data structure implementation.
    """

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = OrderedDict()

    def get(self, key: int) -> int:
        """Get value by key. O(1) time complexity."""
        if key not in self.cache:
            return -1

        # Move to end (most recently used)
        self.cache.move_to_end(key)
        return self.cache[key]

    def put(self, key: int, value: int) -> None:
        """Put key-value pair. O(1) time complexity."""
        if key in self.cache:
            # Update existing key and move to end
            self.cache[key] = value
            self.cache.move_to_end(key)
        else:
            # Add new key
            if len(self.cache) >= self.capacity:
                # Remove least recently used (first item)
                self.cache.popitem(last=False)

            self.cache[key] = value
