from collections import defaultdict

def group_anagrams(words):
    """Group anagrams together using sorted characters as key."""
    anagram_groups = defaultdict(list)
    for word in words:
        sorted_word = ''.join(sorted(word))
        anagram_groups[sorted_word].append(word)
    return list(anagram_groups.values())

def are_anagrams(word1, word2):
    """Helper function to check if two words are anagrams."""
    return sorted(word1) == sorted(word2)

def verify_anagram_groups(groups):
    """Verify that each group contains only anagrams of each other."""
    for group in groups:
        if len(group) <= 1:
            continue  # Single word groups are trivially correct
        
        # Check that all words in the group are anagrams of each other
        first_word = group[0]
        for word in group[1:]:
            if not are_anagrams(first_word, word):
                return False, f"Words '{first_word}' and '{word}' are not anagrams but in same group"
    
    return True, "All groups contain valid anagrams"

def verify_no_missing_anagrams(original_words, groups):
    """Verify that no anagrams are split across different groups."""
    # Flatten all groups
    grouped_words = []
    for group in groups:
        grouped_words.extend(group)
    
    # Check that we have all original words
    if sorted(grouped_words) != sorted(original_words):
        return False, "Some words are missing or duplicated in groups"
    
    # Check that no anagrams are in different groups
    for i, word1 in enumerate(original_words):
        for j, word2 in enumerate(original_words):
            if i != j and are_anagrams(word1, word2):
                # Find which groups they belong to
                group1_idx = None
                group2_idx = None
                
                for g_idx, group in enumerate(groups):
                    if word1 in group:
                        group1_idx = g_idx
                    if word2 in group:
                        group2_idx = g_idx
                
                if group1_idx != group2_idx:
                    return False, f"Anagrams '{word1}' and '{word2}' are in different groups"
    
    return True, "No anagrams are split across groups"

def test_group_anagrams():
    """Comprehensive test suite for group_anagrams function."""
    
    test_cases = [
        # (input, expected_groups_count, description)
        (["eat", "tea", "tan", "ate", "nat", "bat"], 3, "Standard example"),
        ([""], 1, "Single empty string"),
        (["a"], 1, "Single character"),
        (["abc", "bca", "cab", "xyz"], 2, "Two anagram groups"),
        (["abc", "def", "ghi"], 3, "No anagrams"),
        (["aab", "aba", "baa"], 1, "All same characters"),
        (["listen", "silent", "enlist"], 1, "Longer anagrams"),
        (["abc", "bac", "cab", "acb", "bca", "cba"], 1, "All permutations"),
        ([], 0, "Empty input"),
        (["a", "aa", "aaa"], 3, "Different lengths"),
        (["ab", "ba", "abc", "bca", "cab"], 2, "Mixed lengths with anagrams"),
    ]
    
    print("COMPREHENSIVE GROUP ANAGRAMS TESTS")
    print("=" * 50)
    
    all_passed = True
    
    for i, (words, expected_groups, description) in enumerate(test_cases, 1):
        print(f"Test {i:2d}: {description}")
        print(f"         Input: {words}")
        
        result = group_anagrams(words)
        
        # Basic checks
        groups_count = len(result)
        groups_match = groups_count == expected_groups
        
        print(f"         Output: {result}")
        print(f"         Groups count: {groups_count}, Expected: {expected_groups}")
        
        if not words:  # Empty input case
            passed = groups_count == 0
            print(f"         Result: {'✅ PASS' if passed else '❌ FAIL'}")
            all_passed = all_passed and passed
            print()
            continue
        
        # Verify anagram correctness
        valid_groups, group_msg = verify_anagram_groups(result)
        no_split_anagrams, split_msg = verify_no_missing_anagrams(words, result)
        
        passed = groups_match and valid_groups and no_split_anagrams
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"         Groups count correct: {groups_match}")
        print(f"         Valid anagram groups: {valid_groups}")
        print(f"         No split anagrams: {no_split_anagrams}")
        print(f"         Result: {status}")
        
        if not passed:
            if not valid_groups:
                print(f"         Error: {group_msg}")
            if not no_split_anagrams:
                print(f"         Error: {split_msg}")
        
        print()
    
    print(f"OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def test_edge_cases():
    """Test specific edge cases."""
    
    print("\nEDGE CASE TESTS")
    print("=" * 30)
    
    # Test case sensitivity
    result1 = group_anagrams(["Eat", "Tea", "eat"])
    print(f"Case sensitivity: {result1}")
    print(f"Expected: 'Eat'/'Tea' separate from 'eat': {len(result1) >= 2}")
    
    # Test with special characters
    result2 = group_anagrams(["a!b", "b!a", "ab!", "!ab"])
    print(f"Special characters: {result2}")
    print(f"All should be anagrams: {len(result2) == 1}")
    
    # Test with numbers as strings
    result3 = group_anagrams(["123", "321", "132", "456"])
    print(f"Numeric strings: {result3}")
    print(f"Expected 2 groups: {len(result3) == 2}")

def analyze_algorithm():
    """Analyze the algorithm's approach."""
    
    print("\nALGORITHM ANALYSIS")
    print("=" * 30)
    
    print("Approach: Sorted character signature")
    print("Time Complexity: O(n * m log m) where n = number of words, m = average word length")
    print("Space Complexity: O(n * m) for storing all words and keys")
    print()
    print("How it works:")
    print("1. For each word, create a sorted character signature")
    print("2. Use this signature as a key in a hash map")
    print("3. Words with the same signature are anagrams")
    print("4. Return all groups as a list of lists")

if __name__ == "__main__":
    test_group_anagrams()
    test_edge_cases()
    analyze_algorithm()
