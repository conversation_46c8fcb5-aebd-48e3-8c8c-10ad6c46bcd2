from datetime import datetime, timedelta
from collections import defaultdict, deque
import heapq
from typing import Dict, List, Tuple, Set, Optional

class AdvancedSocialEventsTracker:
    """
    Advanced social events tracker with multiple features:
    - Real-time sliding window
    - Event type filtering
    - User engagement metrics
    - Memory-efficient cleanup
    """
    
    def __init__(self, window_minutes: int = 10):
        self.window_minutes = window_minutes
        self.window_duration = timedelta(minutes=window_minutes)
        
        # Store events with automatic cleanup
        self.events = deque()  # (timestamp, user_id, event_type)
        
        # Track user activity for quick lookups
        self.active_users = set()
        
        # Event type statistics
        self.event_type_counts = defaultdict(int)
        
        # User engagement tracking
        self.user_event_counts = defaultdict(int)
        
    def add_event(self, user_id: str, timestamp: str, event_type: str) -> Dict:
        """
        Add event and return comprehensive metrics.
        
        Returns:
            Dictionary with unique_users, total_events, event_types, etc.
        """
        event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Add new event
        self.events.append((event_time, user_id, event_type))
        self.active_users.add(user_id)
        self.event_type_counts[event_type] += 1
        self.user_event_counts[user_id] += 1
        
        # Clean up old events
        self._cleanup_old_events(event_time)
        
        return self._get_current_metrics()
    
    def _cleanup_old_events(self, current_time: datetime):
        """Remove events outside sliding window and update counters."""
        cutoff_time = current_time - self.window_duration
        
        while self.events and self.events[0][0] < cutoff_time:
            old_time, old_user, old_event_type = self.events.popleft()
            
            # Update counters
            self.event_type_counts[old_event_type] -= 1
            if self.event_type_counts[old_event_type] == 0:
                del self.event_type_counts[old_event_type]
            
            self.user_event_counts[old_user] -= 1
            if self.user_event_counts[old_user] == 0:
                del self.user_event_counts[old_user]
                self.active_users.discard(old_user)
    
    def _get_current_metrics(self) -> Dict:
        """Get comprehensive metrics for current window."""
        return {
            'unique_users': len(self.active_users),
            'total_events': len(self.events),
            'event_types': dict(self.event_type_counts),
            'most_active_users': self._get_most_active_users(5),
            'window_start': (self.events[0][0].isoformat() if self.events else None),
            'window_end': (self.events[-1][0].isoformat() if self.events else None)
        }
    
    def _get_most_active_users(self, top_n: int = 5) -> List[Tuple[str, int]]:
        """Get top N most active users in current window."""
        return sorted(self.user_event_counts.items(), 
                     key=lambda x: x[1], reverse=True)[:top_n]
    
    def get_unique_users_by_event_type(self, event_type: str) -> int:
        """Count unique users for specific event type in current window."""
        unique_users = set()
        for _, user_id, e_type in self.events:
            if e_type == event_type:
                unique_users.add(user_id)
        return len(unique_users)
    
    def get_user_activity(self, user_id: str) -> Dict:
        """Get activity summary for specific user in current window."""
        user_events = []
        event_types = defaultdict(int)
        
        for timestamp, uid, event_type in self.events:
            if uid == user_id:
                user_events.append((timestamp.isoformat(), event_type))
                event_types[event_type] += 1
        
        return {
            'total_events': len(user_events),
            'event_types': dict(event_types),
            'events': user_events,
            'is_active': user_id in self.active_users
        }


class OptimizedSlidingWindow:
    """
    Memory and time optimized sliding window using heap for automatic cleanup.
    Best for high-throughput scenarios.
    """
    
    def __init__(self, window_minutes: int = 10):
        self.window_duration = timedelta(minutes=window_minutes)
        
        # Min-heap for automatic cleanup (timestamp, user_id, event_type)
        self.events_heap = []
        
        # Current active users (for O(1) unique count)
        self.active_users = set()
        
        # Track when each user was last seen (for cleanup)
        self.user_last_seen = {}
    
    def add_event(self, user_id: str, timestamp: str, event_type: str) -> int:
        """Add event and return unique user count. Optimized for speed."""
        event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Add to heap
        heapq.heappush(self.events_heap, (event_time, user_id, event_type))
        
        # Update user tracking
        self.active_users.add(user_id)
        self.user_last_seen[user_id] = event_time
        
        # Clean up old events
        self._cleanup_expired_events(event_time)
        
        return len(self.active_users)
    
    def _cleanup_expired_events(self, current_time: datetime):
        """Remove expired events and update active users."""
        cutoff_time = current_time - self.window_duration
        
        # Remove expired events from heap
        while self.events_heap and self.events_heap[0][0] < cutoff_time:
            heapq.heappop(self.events_heap)
        
        # Update active users based on remaining events
        current_active = set()
        for event_time, user_id, _ in self.events_heap:
            if event_time >= cutoff_time:
                current_active.add(user_id)
        
        self.active_users = current_active
        
        # Clean up user_last_seen
        self.user_last_seen = {
            user_id: last_seen 
            for user_id, last_seen in self.user_last_seen.items()
            if last_seen >= cutoff_time
        }


def benchmark_implementations():
    """Compare performance of different implementations."""
    import time
    import random
    
    print("Benchmarking different implementations:")
    print("=" * 50)
    
    # Generate test data
    users = [f"user_{i}" for i in range(100)]
    event_types = ["like", "comment", "share", "view"]
    
    events = []
    base_time = datetime(2025, 10, 13, 19, 0, 0)
    
    for i in range(1000):
        user = random.choice(users)
        event_type = random.choice(event_types)
        timestamp = base_time + timedelta(seconds=i*30)  # Event every 30 seconds
        events.append((user, timestamp.isoformat(), event_type))
    
    # Test Advanced Tracker
    print("Testing AdvancedSocialEventsTracker...")
    start_time = time.time()
    advanced_tracker = AdvancedSocialEventsTracker()
    
    for user_id, timestamp, event_type in events:
        metrics = advanced_tracker.add_event(user_id, timestamp, event_type)
    
    advanced_time = time.time() - start_time
    print(f"Advanced Tracker: {advanced_time:.4f} seconds")
    print(f"Final metrics: {metrics}")
    print()
    
    # Test Optimized Tracker
    print("Testing OptimizedSlidingWindow...")
    start_time = time.time()
    optimized_tracker = OptimizedSlidingWindow()
    
    for user_id, timestamp, event_type in events:
        count = optimized_tracker.add_event(user_id, timestamp, event_type)
    
    optimized_time = time.time() - start_time
    print(f"Optimized Tracker: {optimized_time:.4f} seconds")
    print(f"Final unique users: {count}")
    print()
    
    print(f"Performance improvement: {advanced_time/optimized_time:.2f}x faster")


def demo_real_time_tracking():
    """Demonstrate real-time event tracking."""
    print("Real-time Social Events Tracking Demo:")
    print("=" * 50)
    
    tracker = AdvancedSocialEventsTracker(window_minutes=5)  # 5-minute window
    
    # Simulate real-time events
    events = [
        ("alice", "2025-10-13T19:00:00", "comment"),
        ("bob", "2025-10-13T19:01:00", "like"),
        ("alice", "2025-10-13T19:01:30", "share"),
        ("charlie", "2025-10-13T19:02:00", "comment"),
        ("bob", "2025-10-13T19:02:30", "view"),
        ("diana", "2025-10-13T19:03:00", "like"),
        ("alice", "2025-10-13T19:06:00", "comment"),  # Outside 5-min window from first
        ("eve", "2025-10-13T19:07:00", "share"),
    ]
    
    for user_id, timestamp, event_type in events:
        metrics = tracker.add_event(user_id, timestamp, event_type)
        
        print(f"\n📅 Event: {user_id} -> {event_type} at {timestamp}")
        print(f"👥 Unique users in last 5 min: {metrics['unique_users']}")
        print(f"📊 Total events: {metrics['total_events']}")
        print(f"🏆 Most active: {metrics['most_active_users'][:3]}")
        print(f"📈 Event types: {metrics['event_types']}")
    
    # Query specific user activity
    print(f"\n🔍 Alice's activity: {tracker.get_user_activity('alice')}")
    print(f"💬 Unique users who commented: {tracker.get_unique_users_by_event_type('comment')}")


if __name__ == "__main__":
    demo_real_time_tracking()
    print("\n" + "="*70 + "\n")
    benchmark_implementations()
