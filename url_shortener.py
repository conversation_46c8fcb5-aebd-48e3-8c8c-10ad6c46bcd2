class UrlShortener:
    def __init__(self):
        self.url_map = {}  # short_code -> long_url mapping
        self.reverse_map = {}  # long_url -> short_code mapping (optional, for avoiding duplicates)
        self.counter = 0
        self.base = "http://tinyurl.com/"
        # Base62 characters: 0-9, a-z, A-Z (62 total characters)
        self.base62_chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

    def _to_base62(self, num):
        """Convert a number to base62 string representation."""
        if num == 0:
            return self.base62_chars[0]

        result = []
        while num > 0:
            result.append(self.base62_chars[num % 62])
            num //= 62

        return ''.join(reversed(result))

    def _from_base62(self, base62_str):
        """Convert a base62 string back to number."""
        num = 0
        for char in base62_str:
            num = num * 62 + self.base62_chars.index(char)
        return num

    def encode(self, long_url):
        """
        Encode a long URL to a short URL using counter-based Base62 encoding.

        Time Complexity: O(log n) where n is the counter value
        Space Complexity: O(1) for encoding operation
        """
        # Check if URL already exists to avoid duplicates (optional)
        if long_url in self.reverse_map:
            return self.base + self.reverse_map[long_url]

        # Generate short code using counter and base62 encoding
        short_code = self._to_base62(self.counter)
        short_url = self.base + short_code

        # Store mappings
        self.url_map[short_code] = long_url
        self.reverse_map[long_url] = short_code

        # Increment counter for next URL
        self.counter += 1

        return short_url

    def decode(self, short_url):
        """
        Decode a short URL back to the original long URL.

        Time Complexity: O(1)
        Space Complexity: O(1)
        """
        if not short_url.startswith(self.base):
            return None

        short_code = short_url[len(self.base):]
        return self.url_map.get(short_code, None)


url_shortener = UrlShortener()
for i in range(100):
    short_url = url_shortener.encode(f"https://leetcode.com/problems/design-tinyurl_{i}")
    print(short_url)
    print(url_shortener.decode(short_url))