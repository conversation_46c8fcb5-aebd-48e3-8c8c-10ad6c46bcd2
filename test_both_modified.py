def binary_search_left(nums, target):
    if not nums:
        return -1
    
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    
    if left < len(nums) and nums[left] == target:
        return left
    else:
        return -1

def binary_search_right(nums, target):
    if not nums:
        return -1
    
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target >= nums[mid]:
            left = mid + 1
        else:
            right = mid - 1
    
    if right >= 0 and nums[right] == target:
        return right
    else:
        return -1

def count_occurrences_of_k_in_sorted_array(nums, k):
    left_idx = binary_search_left(nums, k)
    if left_idx == -1:
        return 0
    
    right_idx = binary_search_right(nums, k)
    if right_idx == -1:
        return 0
    
    return right_idx - left_idx + 1

# Test cases
test_cases = [
    ([1, 2, 3, 4, 4, 4, 5, 6], 4, 3),
    ([1, 2, 3, 4, 4, 4, 5, 6], 7, 0),
    ([1, 2, 3, 4, 4, 4, 5, 6], 0, 0),
    ([1, 1, 1, 1, 1], 1, 5),
    ([5], 5, 1),
    ([5], 3, 0),
    ([], 1, 0),
    ([1, 2, 2, 2, 3], 2, 3),
]

print("Testing both modified binary search functions:")
print("=" * 50)

for nums, target, expected in test_cases:
    left = binary_search_left(nums, target)
    right = binary_search_right(nums, target)
    count = count_occurrences_of_k_in_sorted_array(nums, target)
    manual = nums.count(target)
    
    status = "✅" if count == expected == manual else "❌"
    
    print(f"{status} Array: {nums}, Target: {target}")
    print(f"   Left: {left}, Right: {right}, Count: {count}")
    print(f"   Expected: {expected}, Manual: {manual}")
    print()
