def sliding_window_average(nums, k):
    if not nums:
        return []
    if k == 0:
        return nums
    result = []
    window_sum = 0
    for i in range(len(nums)):
        if i >= k:
            window_sum -= nums[i - k]
        window_sum += nums[i]
        num_elements = min(i + 1, k)
        result.append(window_sum / num_elements)
    return result

print(sliding_window_average([10, 20, 30, 40], 3))