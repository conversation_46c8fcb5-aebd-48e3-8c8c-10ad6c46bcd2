def binary_search_left_modified(nums, target):
    """
    Modified binary_search_left that returns -1 if target doesn't exist.
    """
    if not nums:
        return -1
    
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    
    # Check if target actually exists at the found position
    if left < len(nums) and nums[left] == target:
        return left
    else:
        return -1

def binary_search_left_original(nums, target):
    """
    Original binary_search_left that returns insertion point.
    """
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    return left

def test_modified_binary_search_left():
    """Test the modified binary_search_left function."""
    
    test_cases = [
        # (array, target, expected_result, description)
        ([1, 2, 2, 2, 3, 4, 5], 2, 1, "Multiple occurrences - should return first"),
        ([1, 2, 2, 2, 3, 4, 5], 1, 0, "Target at beginning"),
        ([1, 2, 2, 2, 3, 4, 5], 5, 6, "Target at end"),
        ([1, 2, 2, 2, 3, 4, 5], 3, 4, "Single occurrence in middle"),
        ([1, 2, 2, 2, 3, 4, 5], 6, -1, "Target larger than all elements"),
        ([1, 2, 2, 2, 3, 4, 5], 0, -1, "Target smaller than all elements"),
        ([1, 2, 2, 2, 3, 4, 5], 2.5, -1, "Target between elements"),
        ([5], 5, 0, "Single element - found"),
        ([5], 3, -1, "Single element - not found"),
        ([], 1, -1, "Empty array"),
        ([1, 1, 1, 1, 1], 1, 0, "All elements same"),
        ([1, 3, 5, 7, 9], 4, -1, "Target not in array"),
        ([1, 3, 5, 7, 9], 5, 2, "Target in middle"),
        ([2, 2, 2], 2, 0, "All duplicates"),
        ([1, 2, 3, 4, 5], 1, 0, "First in no-duplicate array"),
        ([1, 2, 3, 4, 5], 5, 4, "Last in no-duplicate array"),
    ]
    
    print("TESTING MODIFIED binary_search_left")
    print("=" * 50)
    print("Modified version returns -1 if target doesn't exist")
    print("=" * 50)
    
    all_passed = True
    
    for i, (nums, target, expected, description) in enumerate(test_cases, 1):
        result = binary_search_left_modified(nums, target)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i:2d}: {status} - {description}")
        print(f"         Array: {nums}")
        print(f"         Target: {target}")
        print(f"         Expected: {expected}, Got: {result}")
        
        # Verify the result is correct
        if result != -1:
            if result < len(nums) and nums[result] == target:
                # Check if it's truly the first occurrence
                is_first = result == 0 or nums[result - 1] != target
                print(f"         Verification: nums[{result}] = {nums[result]}, Is first: {is_first}")
                if not is_first:
                    print(f"         ❌ ERROR: Not the first occurrence!")
                    all_passed = False
            else:
                print(f"         ❌ ERROR: Invalid index or wrong value!")
                all_passed = False
        else:
            # Verify target really doesn't exist
            exists = target in nums
            if exists:
                print(f"         ❌ ERROR: Target exists but function returned -1!")
                all_passed = False
            else:
                print(f"         ✅ Correctly identified target doesn't exist")
        
        print()
    
    print(f"OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def compare_with_original():
    """Compare modified version with original version."""
    
    print("\nCOMPARISON: Modified vs Original")
    print("=" * 40)
    
    test_cases = [
        ([1, 2, 2, 2, 3], 2),    # Exists
        ([1, 2, 2, 2, 3], 4),    # Doesn't exist
        ([1, 3, 5, 7, 9], 6),    # Doesn't exist, between elements
        ([], 1),                 # Empty array
        ([5], 5),                # Single element, exists
        ([5], 3),                # Single element, doesn't exist
    ]
    
    for nums, target in test_cases:
        original_result = binary_search_left_original(nums, target)
        modified_result = binary_search_left_modified(nums, target)
        
        print(f"Array: {nums}, Target: {target}")
        print(f"  Original (insertion point): {original_result}")
        print(f"  Modified (first occurrence or -1): {modified_result}")
        
        # Check if target exists
        exists = target in nums if nums else False
        print(f"  Target exists: {exists}")
        
        if exists:
            expected_first = nums.index(target)
            print(f"  Expected first occurrence: {expected_first}")
            print(f"  Modified correct: {modified_result == expected_first}")
        else:
            print(f"  Modified correctly returns -1: {modified_result == -1}")
        
        print()

def demonstrate_behavior():
    """Demonstrate the behavior difference."""
    
    print("\nBEHAVIOR DEMONSTRATION")
    print("=" * 30)
    
    nums = [1, 3, 5, 7, 9]
    targets = [3, 4, 6, 10]
    
    print(f"Array: {nums}")
    print()
    
    for target in targets:
        original = binary_search_left_original(nums, target)
        modified = binary_search_left_modified(nums, target)
        exists = target in nums
        
        print(f"Target: {target}")
        print(f"  Exists in array: {exists}")
        print(f"  Original result: {original} ({'insertion point' if not exists else 'first occurrence'})")
        print(f"  Modified result: {modified} ({'first occurrence' if exists else 'not found'})")
        print()

if __name__ == "__main__":
    test_modified_binary_search_left()
    compare_with_original()
    demonstrate_behavior()
