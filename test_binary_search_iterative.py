def binary_search_iterative(nums, target):
    """Iterative binary search implementation to test."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target == nums[mid]:
            return mid
        elif target < nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    return -1

def binary_search_iterative_with_debug(nums, target):
    """Same implementation but with debug output."""
    print(f"Searching for {target} in {nums}")
    left, right = 0, len(nums) - 1
    iteration = 0
    
    while left <= right:
        iteration += 1
        mid = (left + right) // 2
        print(f"  Iteration {iteration}: left={left}, right={right}, mid={mid}, nums[mid]={nums[mid]}")
        
        if target == nums[mid]:
            print(f"  Found! Returning {mid}")
            return mid
        elif target < nums[mid]:
            print(f"  {target} < {nums[mid]}, searching left half")
            right = mid - 1
        else:
            print(f"  {target} > {nums[mid]}, searching right half")
            left = mid + 1
    
    print(f"  Not found! Returning -1")
    return -1

def test_binary_search_iterative():
    """Comprehensive test suite for binary_search_iterative."""
    
    test_cases = [
        # (array, target, expected_result, description)
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 5, 4, "Target in middle"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 1, 0, "Target at beginning"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 9, 8, "Target at end"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 10, -1, "Target larger than all"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 0, -1, "Target smaller than all"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], 3.5, -1, "Target between elements"),
        ([5], 5, 0, "Single element - found"),
        ([5], 3, -1, "Single element - not found"),
        ([], 1, -1, "Empty array"),
        ([1, 3, 5, 7, 9, 11], 7, 3, "Even length array"),
        ([1, 3, 5, 7, 9], 5, 2, "Odd length array"),
        ([2, 4, 6, 8, 10, 12, 14], 2, 0, "First element in even array"),
        ([2, 4, 6, 8, 10, 12, 14], 14, 6, "Last element in even array"),
    ]
    
    print("COMPREHENSIVE BINARY SEARCH ITERATIVE TESTS")
    print("=" * 60)
    
    all_passed = True
    
    for i, (nums, target, expected, description) in enumerate(test_cases, 1):
        result = binary_search_iterative(nums, target)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i:2d}: {status} - {description}")
        print(f"         Array: {nums}")
        print(f"         Target: {target}, Expected: {expected}, Got: {result}")
        
        # Additional verification for found elements
        if result != -1 and result < len(nums):
            actual_value = nums[result]
            value_correct = actual_value == target
            print(f"         Verification: nums[{result}] = {actual_value}, Correct: {value_correct}")
            if not value_correct:
                all_passed = False
                print(f"         ❌ ERROR: Found wrong element!")
        
        print()
    
    print(f"OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def test_edge_cases():
    """Test specific edge cases that might break binary search."""
    
    print("\nEDGE CASE TESTS")
    print("=" * 30)
    
    # Test with duplicates (should find any occurrence)
    nums_with_dups = [1, 2, 2, 2, 3, 4, 5]
    target = 2
    result = binary_search_iterative(nums_with_dups, target)
    print(f"Duplicates test: {nums_with_dups}, target={target}")
    print(f"Result: {result}, nums[{result}] = {nums_with_dups[result] if result != -1 else 'N/A'}")
    print(f"Valid: {result != -1 and nums_with_dups[result] == target}")
    
    # Test potential integer overflow (though Python handles big ints well)
    large_nums = list(range(0, 1000000, 2))  # Even numbers 0 to 999998
    large_target = 500000
    result_large = binary_search_iterative(large_nums, large_target)
    expected_large = large_target // 2  # Since we're using even numbers
    print(f"\nLarge array test: Array size {len(large_nums)}, target={large_target}")
    print(f"Result: {result_large}, Expected: {expected_large}")
    print(f"Correct: {result_large == expected_large}")

def demonstrate_algorithm():
    """Demonstrate the algorithm step by step."""
    
    print("\nALGORITHM DEMONSTRATION")
    print("=" * 40)
    
    # Example 1: Target found
    print("Example 1: Target found")
    binary_search_iterative_with_debug([1, 3, 5, 7, 9, 11, 13], 7)
    
    print("\nExample 2: Target not found")
    binary_search_iterative_with_debug([1, 3, 5, 7, 9, 11, 13], 6)

if __name__ == "__main__":
    # Run all tests
    test_binary_search_iterative()
    test_edge_cases()
    demonstrate_algorithm()
