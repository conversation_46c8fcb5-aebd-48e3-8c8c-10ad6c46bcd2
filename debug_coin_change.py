from coin_change import coin_change

def debug_specific_case():
    """Debug the failing test case: coins=[2, 5, 10], amount=18"""
    
    coins = [2, 5, 10]
    amount = 18
    
    print(f"Debugging: coins={coins}, amount={amount}")
    print("Expected: 4 coins")
    
    result = coin_change(coins, amount)
    print(f"Got: {result} coins")
    
    # Let's manually check what the optimal solution should be
    print("\nPossible combinations to make 18:")
    print("- 9 coins of 2: 2*9 = 18")
    print("- 1 coin of 10 + 4 coins of 2: 10 + 2*4 = 18 (5 coins)")
    print("- 2 coins of 5 + 4 coins of 2: 5*2 + 2*4 = 18 (6 coins)")
    print("- 3 coins of 5 + 1 coin of 2 + 1 coin of 1: NOT POSSIBLE (no coin 1)")
    print("- 1 coin of 10 + 1 coin of 5 + 1 coin of 2 + 1 coin of 1: NOT POSSIBLE (no coin 1)")
    
    print("\nActually, let me recalculate:")
    print("- 9 coins of 2: 2*9 = 18 (9 coins)")
    print("- 1 coin of 10 + 4 coins of 2: 10 + 8 = 18 (5 coins)")
    print("- 2 coins of 5 + 4 coins of 2: 10 + 8 = 18 (6 coins)")
    print("- 3 coins of 5 + ? : 15 + ? = 18, need 3 more, but 3 is not divisible by 2 or 5 or 10")
    print("- 1 coin of 10 + 1 coin of 5 + ? : 15 + ? = 18, need 3 more, not possible")
    
    print(f"\nSo the minimum should be 5 coins (10 + 2 + 2 + 2 + 2), not 4!")
    print("The test case expectation might be wrong.")
    
    # Let's verify by checking if 4 coins is even possible
    print("\nChecking if 4 coins is possible:")
    for a in range(5):  # max 4 coins of 10
        for b in range(5):  # max 4 coins of 5  
            for c in range(10):  # max 9 coins of 2
                if a + b + c <= 4 and 10*a + 5*b + 2*c == 18:
                    print(f"Found 4-coin solution: {a} tens + {b} fives + {c} twos = {10*a + 5*b + 2*c}")
                    return
    
    print("No 4-coin solution exists. The test expectation is incorrect.")

def verify_manual_calculation():
    """Manually verify the DP calculation for this case."""
    coins = [2, 5, 10]
    amount = 18
    
    print(f"\nManual DP calculation for coins={coins}, amount={amount}:")
    
    # Initialize DP array
    dp = [float('inf')] * (amount + 1)
    dp[0] = 0
    
    print("DP array initialization:")
    print(f"dp[0] = 0")
    
    for i in range(1, amount + 1):
        for coin in coins:
            if i >= coin:
                old_val = dp[i]
                dp[i] = min(dp[i], dp[i - coin] + 1)
                if dp[i] != old_val:
                    print(f"dp[{i}] = min({old_val}, dp[{i-coin}] + 1) = min({old_val}, {dp[i-coin]} + 1) = {dp[i]} (using coin {coin})")
    
    print(f"\nFinal result: dp[{amount}] = {dp[amount]}")
    return dp[amount]

if __name__ == "__main__":
    debug_specific_case()
    verify_manual_calculation()
