def binary_search_left(nums, target):
    if not nums:
        return -1

    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1

    # Check if target actually exists at the found position
    if left < len(nums) and nums[left] == target:
        return left
    else:
        return -1

def binary_search_right(nums, target):
    if not nums:
        return -1

    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target >= nums[mid]:
            left = mid + 1
        else:
            right = mid - 1

    if right >= 0 and nums[right] == target:
        return right
    else:
        return -1

  
def count_occurrences_of_k_in_sorted_array(nums, k):
    left_idx = binary_search_left(nums, k)
    if left_idx == -1:  # Target doesn't exist
        return 0

    right_idx = binary_search_right(nums, k)
    if right_idx == -1:  # Target doesn't exist
        return 0

    return right_idx - left_idx + 1


print(count_occurrences_of_k_in_sorted_array([1, 2, 3, 4, 4, 4, 5, 6], 4))


def binary_search(nums, target, start, end):
    if start > end:
        return -1
    mid = (start + end) // 2
    if target == nums[mid]:
        return mid
    elif target < nums[mid]:
        return binary_search(nums, target, start, mid - 1)
    else:
        return binary_search(nums, target, mid + 1, end)
    
def binary_search_iterative(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target == nums[mid]:
            return mid
        elif target < nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    return -1