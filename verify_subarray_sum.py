from subarray_sum_equals_k import subarray_sum_equals_k

def brute_force_subarray_sum(nums, k):
    """
    Brute force solution to verify correctness.
    Time complexity: O(n^2)
    """
    count = 0
    n = len(nums)
    
    for i in range(n):
        current_sum = 0
        for j in range(i, n):
            current_sum += nums[j]
            if current_sum == k:
                count += 1
    
    return count

def verify_correctness():
    """Comprehensive verification of subarray_sum_equals_k function."""
    
    test_cases = [
        # Basic cases
        ([], 0, 0),  # Empty array
        ([1], 1, 1),  # Single element match
        ([1], 2, 0),  # Single element no match
        
        # Simple cases
        ([1, 1, 1], 2, 2),  # Your test case
        ([1, 2, 3], 3, 2),  # [3] and [1,2]
        ([1, -1, 0], 0, 3),  # [1,-1], [0], [1,-1,0]
        
        # Edge cases with zeros
        ([0, 0, 0], 0, 6),  # All combinations of zeros
        ([1, 0, 1], 1, 4),  # [1], [0,1], [1], [1,0,1]
        
        # Negative numbers
        ([-1, -1, 1], 0, 1),  # [-1,-1,1]
        ([1, -1, 1, -1], 0, 4),  # [1,-1], [1,-1,1,-1], [-1,1], [-1,1,-1]
        
        # Larger arrays
        ([1, 2, 3, 4, 5], 5, 2),  # [5] and [2,3]
        ([1, 1, 1, 1, 1], 3, 3),  # Three ways to get sum 3
        
        # No matches
        ([1, 2, 3], 10, 0),
        
        # All same numbers
        ([2, 2, 2, 2], 4, 3),  # [2,2] appears 3 times
        
        # Mixed positive/negative
        ([3, 4, 7, 2, -3, 1, 4, 2], 7, 4),  # Multiple subarrays sum to 7
    ]
    
    print("Verifying correctness of subarray_sum_equals_k...")
    print("=" * 60)
    
    all_passed = True
    
    for i, (nums, k, expected) in enumerate(test_cases):
        # Test optimized function
        result = subarray_sum_equals_k(nums, k)
        
        # Verify with brute force
        brute_force_result = brute_force_subarray_sum(nums, k)
        
        if result == expected and result == brute_force_result:
            nums_display = str(nums) if len(nums) <= 8 else f"{nums[:5]}...+{len(nums)-5} more"
            print(f"✅ Test {i+1:2d}: {nums_display}, k={k} -> {result}")
        else:
            print(f"❌ Test {i+1:2d}: {nums}, k={k}")
            print(f"    Expected: {expected}, Got: {result}, Brute force: {brute_force_result}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The subarray_sum_equals_k function is CORRECT!")
    else:
        print("❌ Some tests failed.")
    
    return all_passed

def test_edge_cases():
    """Test specific edge cases that might cause issues."""
    print("\nTesting edge cases...")
    print("-" * 40)
    
    edge_cases = [
        ("Empty array", [], 5),
        ("Single zero", [0], 0),
        ("All zeros", [0, 0, 0, 0], 0),
        ("Large positive k", [1, 2, 3], 100),
        ("Large negative k", [1, 2, 3], -100),
        ("Mixed signs", [-1, 1, -1, 1], 0),
        ("Large array", list(range(1, 21)), 10),  # 1 to 20
    ]
    
    for name, nums, k in edge_cases:
        result = subarray_sum_equals_k(nums, k)
        brute_force_result = brute_force_subarray_sum(nums, k)
        
        if result == brute_force_result:
            print(f"✅ {name}: Found {result} subarrays")
        else:
            print(f"❌ {name}: Optimized={result}, Brute force={brute_force_result}")

def manual_verification():
    """Manually verify some specific cases to understand the algorithm."""
    print("\nManual verification of specific cases:")
    print("-" * 45)
    
    # Case 1: [1, 1, 1], k=2
    print("Case 1: [1, 1, 1], k=2")
    print("Expected subarrays: [1,1] at indices (0,1) and [1,1] at indices (1,2)")
    result1 = subarray_sum_equals_k([1, 1, 1], 2)
    print(f"Result: {result1} (expected: 2)\n")
    
    # Case 2: [1, 2, 3], k=3  
    print("Case 2: [1, 2, 3], k=3")
    print("Expected subarrays: [3] at index 2 and [1,2] at indices (0,1)")
    result2 = subarray_sum_equals_k([1, 2, 3], 3)
    print(f"Result: {result2} (expected: 2)\n")
    
    # Case 3: [1, -1, 0], k=0
    print("Case 3: [1, -1, 0], k=0")
    print("Expected subarrays: [1,-1], [0], [1,-1,0]")
    result3 = subarray_sum_equals_k([1, -1, 0], 0)
    print(f"Result: {result3} (expected: 3)")

if __name__ == "__main__":
    verify_correctness()
    test_edge_cases()
    manual_verification()
