from typing import List, Optional

class Node:
    # Constructor to create a new node
    def __init__(self, cost: int):
        self.cost: int = cost
        self.children: List['Node'] = []
        self.parent: Optional['Node'] = None

def get_cheapest_cost(rootNode: Node) -> int:
    """
    Find the minimum cost path from root to any leaf node.

    Time Complexity: O(n) where n is the number of nodes
    Space Complexity: O(h) where h is the height of the tree

    Args:
        rootNode: Root of the tree

    Returns:
        Minimum cost from root to any leaf
    """
    if not rootNode.children:
        return rootNode.cost

    # Use built-in min() with generator expression for better performance
    return rootNode.cost + min(get_cheapest_cost(child) for child in rootNode.children)


def get_cheapest_cost_iterative(rootNode: Node) -> int:
    """
    Iterative version using DFS to avoid recursion stack overflow for deep trees.

    Time Complexity: O(n) where n is the number of nodes
    Space Complexity: O(w) where w is the maximum width of the tree

    Args:
        rootNode: Root of the tree

    Returns:
        Minimum cost from root to any leaf
    """
    if not rootNode:
        return 0

    # Stack stores (node, path_cost_so_far)
    stack = [(rootNode, rootNode.cost)]
    min_cost = float('inf')

    while stack:
        node, current_cost = stack.pop()

        # If leaf node, update minimum cost
        if not node.children:
            min_cost = min(min_cost, current_cost)
        else:
            # Add children to stack with updated costs
            for child in node.children:
                stack.append((child, current_cost + child.cost))

    return min_cost


def get_cheapest_cost_memoized(rootNode: Node) -> int:
    """
    Memoized version for trees where nodes might be revisited.
    Only useful if the tree has shared subtrees (DAG structure).

    Time Complexity: O(n) where n is unique nodes
    Space Complexity: O(n) for memoization cache
    """
    memo = {}

    def dfs(node):
        if id(node) in memo:
            return memo[id(node)]

        if not node.children:
            result = node.cost
        else:
            result = node.cost + min(dfs(child) for child in node.children)

        memo[id(node)] = result
        return result

    return dfs(rootNode)


# Test and compare different implementations
def test_implementations():
    """Test all implementations with various tree structures."""

    # Test case 1: Original example
    print("=== Test Case 1: Original Example ===")
    root1 = Node(0)
    root1.children = [Node(5), Node(3), Node(6)]
    root1.children[0].children = [Node(4), Node(2)]
    root1.children[1].children = [Node(0)]
    root1.children[2].children = [Node(1), Node(5)]

    result1_recursive = get_cheapest_cost(root1)
    result1_iterative = get_cheapest_cost_iterative(root1)
    result1_memoized = get_cheapest_cost_memoized(root1)

    print(f"Recursive: {result1_recursive}")
    print(f"Iterative: {result1_iterative}")
    print(f"Memoized:  {result1_memoized}")
    assert result1_recursive == result1_iterative == result1_memoized
    print("✓ All implementations agree")

    # Test case 2: Single node
    print("\n=== Test Case 2: Single Node ===")
    root2 = Node(10)

    result2_recursive = get_cheapest_cost(root2)
    result2_iterative = get_cheapest_cost_iterative(root2)
    result2_memoized = get_cheapest_cost_memoized(root2)

    print(f"All methods: {result2_recursive}")
    assert result2_recursive == result2_iterative == result2_memoized == 10
    print("✓ Single node test passed")

    # Test case 3: Linear tree (worst case for recursion)
    print("\n=== Test Case 3: Linear Tree ===")
    root3 = Node(1)
    current = root3
    for i in range(2, 6):
        child = Node(i)
        current.children = [child]
        current = child

    result3_recursive = get_cheapest_cost(root3)
    result3_iterative = get_cheapest_cost_iterative(root3)
    result3_memoized = get_cheapest_cost_memoized(root3)

    print(f"Linear tree cost: {result3_recursive}")
    assert result3_recursive == result3_iterative == result3_memoized
    print("✓ Linear tree test passed")

    # Test case 4: Wide tree
    print("\n=== Test Case 4: Wide Tree ===")
    root4 = Node(0)
    root4.children = [Node(i) for i in range(1, 6)]  # 5 children with costs 1,2,3,4,5

    result4_recursive = get_cheapest_cost(root4)
    result4_iterative = get_cheapest_cost_iterative(root4)
    result4_memoized = get_cheapest_cost_memoized(root4)

    print(f"Wide tree cost: {result4_recursive}")
    assert result4_recursive == result4_iterative == result4_memoized == 1  # 0 + min(1,2,3,4,5)
    print("✓ Wide tree test passed")


def performance_comparison():
    """Compare performance of different implementations."""
    import time

    print("\n=== Performance Comparison ===")

    # Create a moderately deep tree
    def create_binary_tree(depth, cost_base=1):
        if depth == 0:
            return Node(cost_base)

        root = Node(cost_base)
        root.children = [
            create_binary_tree(depth - 1, cost_base + 1),
            create_binary_tree(depth - 1, cost_base + 2)
        ]
        return root

    # Test with depth 15 (2^15 - 1 = 32767 nodes)
    test_tree = create_binary_tree(10)

    # Time recursive version
    start = time.time()
    result_recursive = get_cheapest_cost(test_tree)
    time_recursive = time.time() - start

    # Time iterative version
    start = time.time()
    result_iterative = get_cheapest_cost_iterative(test_tree)
    time_iterative = time.time() - start

    # Time memoized version
    start = time.time()
    result_memoized = get_cheapest_cost_memoized(test_tree)
    time_memoized = time.time() - start

    print(f"Tree depth: 10 (1023 nodes)")
    print(f"Recursive: {time_recursive:.6f}s -> {result_recursive}")
    print(f"Iterative: {time_iterative:.6f}s -> {result_iterative}")
    print(f"Memoized:  {time_memoized:.6f}s -> {result_memoized}")

    assert result_recursive == result_iterative == result_memoized
    print("✓ All results match")


if __name__ == "__main__":
    test_implementations()
    performance_comparison()
    print("\n🎉 All tests passed!")