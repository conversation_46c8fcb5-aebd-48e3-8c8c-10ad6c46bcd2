from typing import List, Optional

class Node:
    # Constructor to create a new node
    def __init__(self, cost: int):
        self.cost: int = cost
        self.children: List['Node'] = []
        self.parent: Optional['Node'] = None

def get_cheapest_cost(rootNode: Node) -> int:
    if not rootNode.children:
        return rootNode.cost
    cheapest_cost = float('inf')
    for child in rootNode.children:
        cheapest_cost = min(cheapest_cost, get_cheapest_cost(child))
    return (rootNode.cost + cheapest_cost)


# debug your code below
root = Node(0)
root.children = [Node(5), Node(3), Node(6)]
root.children[0].children = [Node(4), Node(2)]
root.children[1].children = [Node(0)]
root.children[2].children = [Node(1), Node(5)]

print(get_cheapest_cost(root)) 