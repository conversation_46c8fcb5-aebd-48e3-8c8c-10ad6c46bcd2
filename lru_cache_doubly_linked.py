class Node:
    """
    Node for doubly linked list.
    Each node stores key-value pair and pointers to previous/next nodes.
    """
    def __init__(self, key=0, value=0):
        self.key = key
        self.value = value
        self.prev = None
        self.next = None


class LRUCacheDoublyLinked:
    """
    LRU Cache implementation using Doubly Linked List + HashMap.
    
    Achieves true O(1) time complexity for all operations:
    - get(): O(1)
    - put(): O(1)
    
    Data structures used:
    - HashMap (dict): key -> node mapping for O(1) access
    - Doubly Linked List: maintains order from LRU to MRU
    
    List structure:
    head <-> node1 <-> node2 <-> ... <-> nodeN <-> tail
    ^                                              ^
    dummy                                        dummy
    (MRU side)                                 (LRU side)
    """
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = {}  # key -> node mapping
        
        # Create dummy head and tail nodes to simplify edge cases
        self.head = Node()  # Dummy head (most recently used side)
        self.tail = Node()  # Dummy tail (least recently used side)
        
        # Initialize empty list: head <-> tail
        self.head.next = self.tail
        self.tail.prev = self.head
    
    def _add_node(self, node):
        """
        Add node right after head (most recently used position).
        
        Before: head <-> next_node <-> ...
        After:  head <-> node <-> next_node <-> ...
        
        Time: O(1)
        """
        node.prev = self.head
        node.next = self.head.next
        
        self.head.next.prev = node
        self.head.next = node
    
    def _remove_node(self, node):
        """
        Remove an existing node from the linked list.
        
        Before: ... <-> prev <-> node <-> next <-> ...
        After:  ... <-> prev <-> next <-> ...
        
        Time: O(1)
        """
        prev_node = node.prev
        next_node = node.next
        
        prev_node.next = next_node
        next_node.prev = prev_node
    
    def _move_to_head(self, node):
        """
        Move existing node to head (mark as most recently used).
        
        Time: O(1) - combination of two O(1) operations
        """
        self._remove_node(node)
        self._add_node(node)
    
    def _pop_tail(self):
        """
        Remove the last node (least recently used).
        
        Returns the removed node.
        Time: O(1)
        """
        lru_node = self.tail.prev
        self._remove_node(lru_node)
        return lru_node
    
    def get(self, key: int) -> int:
        """
        Get value by key and mark as most recently used.
        
        Time: O(1)
        - O(1) hash table lookup
        - O(1) move to head operation
        
        Args:
            key: The key to look up
            
        Returns:
            Value if key exists, -1 otherwise
        """
        node = self.cache.get(key)
        
        if not node:
            return -1
        
        # Move the accessed node to head (most recently used)
        self._move_to_head(node)
        
        return node.value
    
    def put(self, key: int, value: int) -> None:
        """
        Insert or update key-value pair.
        
        Time: O(1)
        - O(1) hash table operations
        - O(1) linked list operations
        
        Args:
            key: The key to insert/update
            value: The value to store
        """
        node = self.cache.get(key)
        
        if not node:
            # Key doesn't exist - add new node
            new_node = Node(key, value)
            
            # Check if we need to evict LRU item
            if len(self.cache) >= self.capacity:
                # Remove least recently used node
                lru_node = self._pop_tail()
                del self.cache[lru_node.key]
            
            # Add new node to cache and head of list
            self.cache[key] = new_node
            self._add_node(new_node)
            
        else:
            # Key exists - update value and move to head
            node.value = value
            self._move_to_head(node)
    
    def display(self):
        """
        Display current state of cache (for debugging).
        Shows order from MRU to LRU.
        """
        result = []
        current = self.head.next
        
        while current != self.tail:
            result.append(f"({current.key}: {current.value})")
            current = current.next
        
        print(f"Cache (MRU -> LRU): {' -> '.join(result)}")
        print(f"Size: {len(self.cache)}/{self.capacity}")


# Example usage and testing
if __name__ == "__main__":
    # Test the LRU Cache
    cache = LRUCacheDoublyLinked(3)
    
    print("=== LRU Cache Test ===")
    
    # Test puts
    cache.put(1, 10)
    cache.display()  # (1: 10)
    
    cache.put(2, 20)
    cache.display()  # (2: 20) -> (1: 10)
    
    cache.put(3, 30)
    cache.display()  # (3: 30) -> (2: 20) -> (1: 10)
    
    # Test get (should move to front)
    print(f"\nget(2): {cache.get(2)}")  # Should return 20
    cache.display()  # (2: 20) -> (3: 30) -> (1: 10)
    
    # Test capacity overflow (should evict LRU)
    cache.put(4, 40)
    cache.display()  # (4: 40) -> (2: 20) -> (3: 30)  [1 evicted]
    
    # Test update existing key
    cache.put(2, 25)
    cache.display()  # (2: 25) -> (4: 40) -> (3: 30)
    
    # Test get non-existent key
    print(f"\nget(1): {cache.get(1)}")  # Should return -1
    
    print("\n=== Test Complete ===")
