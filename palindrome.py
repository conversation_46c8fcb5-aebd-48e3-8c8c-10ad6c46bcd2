def is_palindrome(s):
    """
    Check if a string is a palindrome using iterative two-pointer approach.

    Time Complexity: O(n) where n is the length of the string
    Space Complexity: O(1) - only uses two pointer variables

    Args:
        s: String to check for palindrome property

    Returns:
        True if string is a palindrome, False otherwise

    Examples:
        is_palindrome("racecar") -> True
        is_palindrome("hello") -> False
        is_palindrome("") -> True
        is_palindrome("a") -> True
    """
    if not s:
        return True

    # Two pointers: one from start, one from end
    left = 0
    right = len(s) - 1

    while left < right:
        if s[left] != s[right]:
            return False
        left += 1
        right -= 1

    return True


def is_palindrome_case_insensitive(s):
    """
    Check if a string is a palindrome (case-insensitive, ignoring spaces/punctuation).

    Args:
        s: String to check

    Returns:
        True if string is a palindrome, False otherwise

    Examples:
        is_palindrome_case_insensitive("A man a plan a canal Panama") -> True
        is_palindrome_case_insensitive("race a car") -> False
    """
    # Clean string: keep only alphanumeric characters, convert to lowercase
    cleaned = ''.join(char.lower() for char in s if char.isalnum())
    return is_palindrome(cleaned)


# Test cases
test_cases = [
    "racecar",
    "hello",
    "madam",
    "a",
    "",
    "abcba",
    "abccba",
    "abc"
]

print("Basic palindrome tests:")
for test in test_cases:
    result = is_palindrome(test)
    print(f'is_palindrome("{test}") = {result}')

print("\nCase-insensitive palindrome tests:")
advanced_tests = [
    "A man a plan a canal Panama",
    "race a car",
    "Was it a car or a cat I saw?",
    "Madam",
    "No 'x' in Nixon"
]

for test in advanced_tests:
    result = is_palindrome_case_insensitive(test)
    print(f'is_palindrome_case_insensitive("{test}") = {result}')