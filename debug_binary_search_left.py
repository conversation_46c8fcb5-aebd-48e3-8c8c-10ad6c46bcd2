def binary_search_left_buggy(nums, target):
    """The buggy version from the original code."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        print(f"  left={left}, right={right}, mid={mid}, nums[mid]={nums[mid]}")
        if target <= nums[mid]:  # BUGGY condition
            print(f"    {target} <= {nums[mid]} is True, moving right to {mid-1}")
            right = mid - 1
        else:
            print(f"    {target} <= {nums[mid]} is False, moving left to {mid+1}")
            left = mid + 1
    print(f"  Final: left={left}")
    return left

def binary_search_left_correct(nums, target):
    """The correct version."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        print(f"  left={left}, right={right}, mid={mid}, nums[mid]={nums[mid]}")
        if nums[mid] >= target:  # CORRECT condition
            print(f"    {nums[mid]} >= {target} is True, moving right to {mid-1}")
            right = mid - 1
        else:
            print(f"    {nums[mid]} >= {target} is False, moving left to {mid+1}")
            left = mid + 1
    print(f"  Final: left={left}")
    return left

# Test case that should expose the difference
nums = [1, 3, 5, 7, 9]
target = 4

print("Testing with nums=[1, 3, 5, 7, 9], target=4")
print("Expected: Should return index 2 (where 4 would be inserted)")
print()

print("BUGGY VERSION:")
buggy_result = binary_search_left_buggy(nums, target)
print(f"Result: {buggy_result}")
print()

print("CORRECT VERSION:")
correct_result = binary_search_left_correct(nums, target)
print(f"Result: {correct_result}")
print()

print(f"Results match: {buggy_result == correct_result}")

# Let's test with a case where target exists
print("\n" + "="*50)
print("Testing with nums=[1, 2, 2, 2, 3], target=2")
print("Expected: Should return index 1 (first occurrence of 2)")
print()

nums2 = [1, 2, 2, 2, 3]
target2 = 2

print("BUGGY VERSION:")
buggy_result2 = binary_search_left_buggy(nums2, target2)
print(f"Result: {buggy_result2}")
print()

print("CORRECT VERSION:")
correct_result2 = binary_search_left_correct(nums2, target2)
print(f"Result: {correct_result2}")
print()

print(f"Results match: {buggy_result2 == correct_result2}")

# Manual verification
print(f"\nManual check: nums2[{buggy_result2}] = {nums2[buggy_result2] if buggy_result2 < len(nums2) else 'OUT_OF_BOUNDS'}")
print(f"Is this the first occurrence? {buggy_result2 == 0 or nums2[buggy_result2-1] != target2}")
