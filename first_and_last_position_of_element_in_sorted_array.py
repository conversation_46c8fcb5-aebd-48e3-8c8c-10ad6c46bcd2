def first_and_last_position_of_element_in_sorted_array(nums, target):
    def binary_search_left(nums, target):
        left, right = 0, len(nums) - 1
        while left <= right:
            mid = (left + right) // 2
            if nums[mid] >= target:
                right = mid - 1
            else:
                left = mid + 1
        return left

    def binary_search_right(nums, target):
        left, right = 0, len(nums) - 1
        while left <= right:
            mid = (left + right) // 2
            if nums[mid] <= target:
                left = mid + 1
            else:
                right = mid - 1
        return right

    left, right = binary_search_left(nums, target), binary_search_right(nums, target)
    if left <= right:
        return [left, right]
    else:
        return [-1, -1]


def first_position_of_element_in_sorted_array(nums, target, left, right):
    """
    Find the first position of target in a sorted array using binary search.

    Args:
        nums: Sorted array
        target: Element to find
        left: Left boundary of search range
        right: Right boundary of search range

    Returns:
        Index of first occurrence of target, or -1 if not found
    """
    if left > right:
        return -1

    mid = (left + right) // 2
    print(f"Searching range [{left}, {right}], mid={mid}, nums[mid]={nums[mid]}")

    if nums[mid] == target:
        # Check if this is the first occurrence
        if mid == 0 or nums[mid - 1] != target:
            print(f"Found first occurrence at index {mid}")
            return mid  # This is the first occurrence
        else:
            # Search left for an earlier occurrence
            print(f"Found target at {mid}, but searching left for first occurrence")
            return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)
    elif nums[mid] < target:
        # Target is in the right half
        print(f"Target > nums[mid], searching right half")
        return first_position_of_element_in_sorted_array(nums, target, mid + 1, right)
    else:
        # Target is in the left half
        print(f"Target < nums[mid], searching left half")
        return first_position_of_element_in_sorted_array(nums, target, left, mid - 1)

nums = [5, 7, 7, 8, 8, 10]
print(first_position_of_element_in_sorted_array(nums, 8, 0, len(nums) - 1))
