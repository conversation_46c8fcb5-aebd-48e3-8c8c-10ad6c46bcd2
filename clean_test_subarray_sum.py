from collections import defaultdict

def subarray_sum_equals_k_clean(nums, k):
    """
    Clean version without debug prints for final verification.
    Find the number of continuous subarrays whose sum equals to k.
    
    Time Complexity: O(n)
    Space Complexity: O(n)
    """
    count = 0
    current_sum = 0
    sum_count = defaultdict(int)
    sum_count[0] = 1  # Initialize with sum 0 having one occurrence

    for num in nums:
        current_sum += num
        if current_sum - k in sum_count:
            count += sum_count[current_sum - k]
        sum_count[current_sum] += 1

    return count

def final_verification():
    """Final verification with clean function."""
    
    test_cases = [
        # Basic test cases
        ([], 0, 0),
        ([1], 1, 1),
        ([1, 1, 1], 2, 2),
        ([1, 2, 3], 3, 2),
        ([1, -1, 0], 0, 3),
        
        # Edge cases
        ([0, 0, 0], 0, 6),
        ([1, 0, 1], 1, 4),
        ([-1, -1, 1], 0, 1),
        ([1, -1, 1, -1], 0, 4),
        
        # Complex cases
        ([3, 4, 7, 2, -3, 1, 4, 2], 7, 4),
        ([1, 2, 1, 2, 1], 3, 4),
        ([-1, 1, 0], 0, 3),
    ]
    
    print("Final Verification (Clean Version)")
    print("=" * 40)
    
    all_passed = True
    
    for i, (nums, k, expected) in enumerate(test_cases):
        result = subarray_sum_equals_k_clean(nums, k)
        
        if result == expected:
            nums_str = str(nums) if len(nums) <= 10 else f"{nums[:7]}...+{len(nums)-7}"
            print(f"✅ Test {i+1:2d}: {nums_str}, k={k} -> {result}")
        else:
            print(f"❌ Test {i+1:2d}: {nums}, k={k} -> {result} (expected: {expected})")
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 FINAL VERIFICATION PASSED!")
        print("✅ subarray_sum_equals_k is CORRECT and OPTIMIZED!")
        print("\nAlgorithm Summary:")
        print("- Time Complexity: O(n)")
        print("- Space Complexity: O(n)")
        print("- Uses prefix sum + hash map technique")
        print("- Handles negative numbers correctly")
        print("- Efficient single-pass solution")
    else:
        print("❌ Final verification failed.")

def algorithm_explanation():
    """Explain how the algorithm works."""
    print("\n" + "="*50)
    print("ALGORITHM EXPLANATION")
    print("="*50)
    print("""
The algorithm uses the prefix sum technique with a hash map:

1. Key Insight: If sum[i:j] = k, then prefix_sum[j] - prefix_sum[i-1] = k
   Therefore: prefix_sum[i-1] = prefix_sum[j] - k

2. For each position j, we check if (current_prefix_sum - k) exists in our hash map
   If it does, it means there are subarrays ending at j with sum = k

3. We maintain a count of how many times each prefix sum has occurred
   This handles cases where the same prefix sum appears multiple times

4. Initialize with sum_count[0] = 1 to handle subarrays starting from index 0

Example: [1, 1, 1], k=2
- i=0: sum=1, check (1-2=-1) not in map, add sum=1
- i=1: sum=2, check (2-2=0) in map (1 time), count+=1, add sum=2  
- i=2: sum=3, check (3-2=1) in map (1 time), count+=1, add sum=3
- Result: 2 subarrays found
""")

if __name__ == "__main__":
    final_verification()
    algorithm_explanation()
