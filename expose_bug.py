# Original buggy implementation
def binary_search_left(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    return left

def binary_search_right(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target >= nums[mid]:
            left = mid + 1
        else:
            right = mid - 1
    return right

def count_occurrences_buggy(nums, k):
    left_idx = binary_search_left(nums, k)
    if left_idx == -1:  # This check is useless
        return 0
    right_idx = binary_search_right(nums, k)
    if right_idx == -1:  # This check is useless
        return 0
    return right_idx - left_idx + 1

# Corrected implementation
def count_occurrences_correct(nums, k):
    if not nums:
        return 0
    
    left_idx = binary_search_left(nums, k)
    right_idx = binary_search_right(nums, k)
    
    # Check if target actually exists
    if left_idx < len(nums) and nums[left_idx] == k:
        return right_idx - left_idx + 1
    else:
        return 0

# Test case that exposes the bug
print("BUG EXPOSURE TEST:")
print("=" * 40)

# Case 1: Target doesn't exist but falls within array bounds
nums1 = [1, 3, 5, 7, 9]
k1 = 4

left = binary_search_left(nums1, k1)
right = binary_search_right(nums1, k1)

print(f"Array: {nums1}")
print(f"Target: {k1} (doesn't exist)")
print(f"binary_search_left returns: {left}")
print(f"binary_search_right returns: {right}")
print(f"nums[{left}] = {nums1[left] if left < len(nums1) else 'OUT_OF_BOUNDS'}")

buggy_count = count_occurrences_buggy(nums1, k1)
correct_count = count_occurrences_correct(nums1, k1)
actual_count = nums1.count(k1)

print(f"\nBuggy function returns: {buggy_count}")
print(f"Correct function returns: {correct_count}")
print(f"Actual count: {actual_count}")
print(f"Buggy is wrong: {buggy_count != actual_count}")

# Case 2: Target doesn't exist and would be inserted at end
print("\n" + "=" * 40)
nums2 = [1, 2, 3, 4, 5]
k2 = 10

left2 = binary_search_left(nums2, k2)
right2 = binary_search_right(nums2, k2)

print(f"Array: {nums2}")
print(f"Target: {k2} (doesn't exist, larger than all)")
print(f"binary_search_left returns: {left2}")
print(f"binary_search_right returns: {right2}")

buggy_count2 = count_occurrences_buggy(nums2, k2)
correct_count2 = count_occurrences_correct(nums2, k2)
actual_count2 = nums2.count(k2)

print(f"\nBuggy function returns: {buggy_count2}")
print(f"Correct function returns: {correct_count2}")
print(f"Actual count: {actual_count2}")
print(f"Buggy is wrong: {buggy_count2 != actual_count2}")

# Case 3: Empty array
print("\n" + "=" * 40)
nums3 = []
k3 = 1

print(f"Array: {nums3}")
print(f"Target: {k3}")

try:
    buggy_count3 = count_occurrences_buggy(nums3, k3)
    print(f"Buggy function returns: {buggy_count3}")
except IndexError as e:
    print(f"Buggy function crashes: {e}")

correct_count3 = count_occurrences_correct(nums3, k3)
print(f"Correct function returns: {correct_count3}")
