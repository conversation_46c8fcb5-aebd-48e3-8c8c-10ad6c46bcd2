from typing import List

def valid_cell(cell, grid):
    return cell[0] < len(grid) and cell[1] < len(grid[0])

def shortestCellPath(grid: List[List[int]], sr: int, sc: int, tr: int, tc: int) -> int:
    shortest_path_len = 0
    queue = [(sr, sc)]
    visited_cells = set()
    while queue:
        cell = queue.popLeft()
        if cell[0] == tr and cell[1] == tc:
            shortest_path_len += 1
            return shortest_path_len
        next_cells = []
        next_cells.append((cell[0] - 1, cell[1]))
        next_cells.append((cell[0] + 1, cell[1]))
        next_cells.append((cell[0], cell[1] - 1))
        next_cells.append((cell[0], cell[1] + 1))
        for next_cell in next_cells:
            if valid_cell(next_cell, grid) and next_cell not in visited_cells:
                visited_cells.add(next_cell)
                queue.push(next_cell)
    return -1
	
# debug your code below
grid = [[1, 1, 1, 1], [0, 0, 0, 1], [1, 1, 1, 1]]
sr, sc, tr, tc = 0, 0, 2, 0
print(shortestCellPath(grid, sr, sc, tr, tc))