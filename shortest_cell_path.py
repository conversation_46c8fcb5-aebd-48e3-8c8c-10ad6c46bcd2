from typing import List
from collections import deque

def valid_cell(cell, grid):
    """Check if cell is within grid bounds and not an obstacle."""
    r, c = cell
    return (0 <= r < len(grid) and
            0 <= c < len(grid[0]) and
            grid[r][c] == 1)  # Assuming 1 = walkable, 0 = obstacle

def shortestCellPath(grid: List[List[int]], sr: int, sc: int, tr: int, tc: int) -> int:
    """
    Find shortest path from (sr, sc) to (tr, tc) using BFS.

    Args:
        grid: 2D grid where 1 = walkable, 0 = obstacle
        sr, sc: Starting row and column
        tr, tc: Target row and column

    Returns:
        Length of shortest path, or -1 if no path exists
    """
    # Edge case: start or target is obstacle
    if not valid_cell((sr, sc), grid) or not valid_cell((tr, tc), grid):
        return -1

    # Edge case: already at target
    if sr == tr and sc == tc:
        return 0

    # BFS setup
    queue = deque([(sr, sc, 0)])  # (row, col, distance)
    visited = set()
    visited.add((sr, sc))

    # Directions: up, down, left, right
    directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]

    while queue:
        row, col, dist = queue.popleft()

        # Check all 4 directions
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            next_cell = (new_row, new_col)

            # Check if we reached the target
            if new_row == tr and new_col == tc:
                return dist + 1

            # Check if valid cell and not visited
            if (valid_cell(next_cell, grid) and
                next_cell not in visited):
                visited.add(next_cell)
                queue.append((new_row, new_col, dist + 1))

    return -1  # No path found
	
def test_shortest_cell_path():
    """Comprehensive tests for shortestCellPath function."""

    def test_case(name, grid, sr, sc, tr, tc, expected):
        print(f"\n--- {name} ---")
        print(f"Grid:")
        for row in grid:
            print(f"  {row}")
        print(f"From ({sr}, {sc}) to ({tr}, {tc})")

        result = shortestCellPath(grid, sr, sc, tr, tc)
        print(f"Result: {result}, Expected: {expected}")

        assert result == expected, f"Expected {expected}, got {result}"
        print("✓ Test passed")

    # Test 1: Original example
    grid1 = [[1, 1, 1, 1],
             [0, 0, 0, 1],
             [1, 1, 1, 1]]
    test_case("Original example", grid1, 0, 0, 2, 0, 8)

    # Test 2: Direct path
    grid2 = [[1, 1, 1],
             [1, 1, 1],
             [1, 1, 1]]
    test_case("Direct path", grid2, 0, 0, 2, 2, 4)

    # Test 3: No path (blocked)
    grid3 = [[1, 0, 1],
             [0, 0, 0],
             [1, 0, 1]]
    test_case("No path", grid3, 0, 0, 2, 2, -1)

    # Test 4: Same start and target
    grid4 = [[1, 1],
             [1, 1]]
    test_case("Same start/target", grid4, 0, 0, 0, 0, 0)

    # Test 5: Start is obstacle
    grid5 = [[0, 1],
             [1, 1]]
    test_case("Start is obstacle", grid5, 0, 0, 1, 1, -1)

    # Test 6: Target is obstacle
    grid6 = [[1, 1],
             [1, 0]]
    test_case("Target is obstacle", grid6, 0, 0, 1, 1, -1)

    # Test 7: Single cell grid
    grid7 = [[1]]
    test_case("Single cell", grid7, 0, 0, 0, 0, 0)

    # Test 8: Maze with longer path
    grid8 = [[1, 0, 1, 1, 1],
             [1, 0, 0, 0, 1],
             [1, 1, 1, 0, 1],
             [0, 0, 1, 0, 1],
             [1, 1, 1, 1, 1]]
    test_case("Complex maze", grid8, 0, 0, 4, 4, 8)  # Corrected expected value


def visualize_path(grid, sr, sc, tr, tc):
    """Visualize the shortest path if it exists."""
    from collections import deque

    print(f"\n=== Path Visualization ===")
    print(f"Finding path from ({sr}, {sc}) to ({tr}, {tc})")

    if not valid_cell((sr, sc), grid) or not valid_cell((tr, tc), grid):
        print("❌ Start or target is invalid")
        return

    if sr == tr and sc == tc:
        print("✅ Already at target")
        return

    # BFS with path tracking
    queue = deque([(sr, sc, 0, [(sr, sc)])])  # (row, col, dist, path)
    visited = set()
    visited.add((sr, sc))

    directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]

    while queue:
        row, col, dist, path = queue.popleft()

        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            next_cell = (new_row, new_col)

            if new_row == tr and new_col == tc:
                final_path = path + [(new_row, new_col)]
                print(f"✅ Path found! Length: {dist + 1}")
                print(f"Path: {' -> '.join(map(str, final_path))}")

                # Create visual grid
                visual_grid = [['.' if cell == 1 else '#' for cell in row] for row in grid]
                for i, (r, c) in enumerate(final_path):
                    if i == 0:
                        visual_grid[r][c] = 'S'  # Start
                    elif i == len(final_path) - 1:
                        visual_grid[r][c] = 'T'  # Target
                    else:
                        visual_grid[r][c] = '*'  # Path

                print("Visual representation:")
                for row in visual_grid:
                    print('  ' + ' '.join(row))
                print("Legend: S=Start, T=Target, *=Path, .=Walkable, #=Obstacle")
                return

            if (valid_cell(next_cell, grid) and
                next_cell not in visited):
                visited.add(next_cell)
                queue.append((new_row, new_col, dist + 1, path + [(new_row, new_col)]))

    print("❌ No path found")


if __name__ == "__main__":
    # Run tests
    test_shortest_cell_path()

    # Visualize original example
    grid = [[1, 1, 1, 1], [0, 0, 0, 1], [1, 1, 1, 1]]
    visualize_path(grid, 0, 0, 2, 0)

    print("\n🎉 All tests passed!")