import time
from longest_palindromic_substring import longest_palindromic_substring

def is_palindrome(s):
    """Helper function for the original O(n³) implementation."""
    return s == s[::-1]

def longest_palindromic_substring_original(s):
    """Original O(n³) implementation for comparison."""
    if not s:
        return ""
    if len(s) == 1:
        return s
    max_length = 0
    start = 0
    end = 0
    for i in range(len(s)):
        for j in range(i, len(s)):
            if is_palindrome(s[i:j+1]):
                if j - i + 1 > max_length:
                    max_length = j - i + 1
                    start = i
                    end = j
    return s[start:end+1]

def compare_performance():
    """Compare performance between original and optimized implementations."""
    test_cases = [
        ("Small string", "racecar" * 5),  # 35 chars
        ("Medium string", "abcdefg" * 10),  # 70 chars  
        ("Large string", "palindrome" * 15),  # 150 chars
        ("Palindrome heavy", "aba" * 30),  # 90 chars with many palindromes
    ]
    
    print("Performance Comparison:")
    print("=" * 80)
    print(f"{'Test Case':<20} {'String Length':<15} {'Original (ms)':<15} {'Optimized (ms)':<15} {'Speedup':<10}")
    print("-" * 80)
    
    for name, test_string in test_cases:
        # Test original implementation
        start_time = time.time()
        result_original = longest_palindromic_substring_original(test_string)
        original_time = (time.time() - start_time) * 1000
        
        # Test optimized implementation
        start_time = time.time()
        result_optimized = longest_palindromic_substring(test_string)
        optimized_time = (time.time() - start_time) * 1000
        
        # Verify both give same length results (may be different palindromes of same length)
        assert len(result_original) == len(result_optimized), f"Results differ in length: {len(result_original)} vs {len(result_optimized)}"
        
        speedup = original_time / optimized_time if optimized_time > 0 else float('inf')
        
        print(f"{name:<20} {len(test_string):<15} {original_time:<15.2f} {optimized_time:<15.2f} {speedup:<10.1f}x")

def complexity_analysis():
    """Demonstrate the complexity difference with larger inputs."""
    print("\n\nComplexity Analysis:")
    print("=" * 50)
    print("Testing with progressively larger strings...")
    
    sizes = [50, 100, 200, 300]
    
    for size in sizes:
        test_string = "a" * (size // 2) + "b" + "a" * (size // 2)  # Palindrome in middle
        
        # Only test optimized for larger sizes to avoid long waits
        if size <= 100:
            start_time = time.time()
            longest_palindromic_substring_original(test_string)
            original_time = (time.time() - start_time) * 1000
        else:
            original_time = "Too slow"
        
        start_time = time.time()
        longest_palindromic_substring(test_string)
        optimized_time = (time.time() - start_time) * 1000
        
        print(f"Size {size:3d}: Original: {original_time:>10}, Optimized: {optimized_time:6.2f}ms")

if __name__ == "__main__":
    compare_performance()
    complexity_analysis()
