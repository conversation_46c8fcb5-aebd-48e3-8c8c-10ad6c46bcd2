def coin_change(coins, amount):
    min_num_coins = [float('inf')] * (amount + 1)
    min_num_coins[0] = 0
    for current_amount in range(1, amount + 1):
        for coin in coins:
            remaining_amount = current_amount - coin
            if remaining_amount >= 0:
                min_num_coins[current_amount] = min(min_num_coins[current_amount], min_num_coins[remaining_amount] + 1)
                print(f"Amount: {current_amount}, Min coins: {min_num_coins[current_amount]}, DP array: {min_num_coins}")
        # print(f"Amount: {current_amount}, Min coins: {min_num_coins[current_amount]}, DP array: {min_num_coins}")
    return min_num_coins[amount] if min_num_coins[amount] != float('inf') else -1

print(coin_change([1, 2, 5], 11))