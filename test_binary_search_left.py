def binary_search_left(nums, target):
    """Find the leftmost (first) occurrence of target in sorted array."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if nums[mid] >= target:
            right = mid - 1
        else:
            left = mid + 1
    return left

def test_binary_search_left():
    """Comprehensive test cases for binary_search_left."""
    
    # Test Case 1: Multiple occurrences
    nums1 = [1, 2, 2, 2, 3, 4, 5]
    target1 = 2
    result1 = binary_search_left(nums1, target1)
    expected1 = 1  # First occurrence of 2 is at index 1
    print(f"Test 1: nums={nums1}, target={target1}")
    print(f"Result: {result1}, Expected: {expected1}, Pass: {result1 == expected1}")
    
    # Test Case 2: Single occurrence
    nums2 = [1, 3, 5, 7, 9]
    target2 = 5
    result2 = binary_search_left(nums2, target2)
    expected2 = 2  # Only occurrence of 5 is at index 2
    print(f"\nTest 2: nums={nums2}, target={target2}")
    print(f"Result: {result2}, Expected: {expected2}, Pass: {result2 == expected2}")
    
    # Test Case 3: Target not in array (insertion point)
    nums3 = [1, 3, 5, 7, 9]
    target3 = 6
    result3 = binary_search_left(nums3, target3)
    expected3 = 3  # 6 would be inserted at index 3
    print(f"\nTest 3: nums={nums3}, target={target3}")
    print(f"Result: {result3}, Expected: {expected3}, Pass: {result3 == expected3}")
    
    # Test Case 4: Target at beginning
    nums4 = [2, 2, 2, 3, 4, 5]
    target4 = 2
    result4 = binary_search_left(nums4, target4)
    expected4 = 0  # First occurrence of 2 is at index 0
    print(f"\nTest 4: nums={nums4}, target={target4}")
    print(f"Result: {result4}, Expected: {expected4}, Pass: {result4 == expected4}")
    
    # Test Case 5: Target at end
    nums5 = [1, 2, 3, 5, 5, 5]
    target5 = 5
    result5 = binary_search_left(nums5, target5)
    expected5 = 3  # First occurrence of 5 is at index 3
    print(f"\nTest 5: nums={nums5}, target={target5}")
    print(f"Result: {result5}, Expected: {expected5}, Pass: {result5 == expected5}")
    
    # Test Case 6: Empty array
    nums6 = []
    target6 = 1
    result6 = binary_search_left(nums6, target6)
    expected6 = 0  # Would be inserted at index 0
    print(f"\nTest 6: nums={nums6}, target={target6}")
    print(f"Result: {result6}, Expected: {expected6}, Pass: {result6 == expected6}")
    
    # Test Case 7: All elements same
    nums7 = [3, 3, 3, 3, 3]
    target7 = 3
    result7 = binary_search_left(nums7, target7)
    expected7 = 0  # First occurrence is at index 0
    print(f"\nTest 7: nums={nums7}, target={target7}")
    print(f"Result: {result7}, Expected: {expected7}, Pass: {result7 == expected7}")

def verify_first_occurrence(nums, target, result):
    """Verify that result is actually the first occurrence."""
    if not nums or result >= len(nums) or nums[result] != target:
        return False
    
    # Check if there's an earlier occurrence
    if result > 0 and nums[result - 1] == target:
        return False
    
    return True

# Run tests
if __name__ == "__main__":
    test_binary_search_left()
    
    # Additional verification
    print("\n" + "="*50)
    print("VERIFICATION: Checking if results are truly first occurrences")
    print("="*50)
    
    test_cases = [
        ([1, 2, 2, 2, 3, 4, 5], 2),
        ([5, 7, 7, 8, 8, 10], 8),
        ([1, 1, 1, 1, 1], 1),
        ([2, 4, 6, 8, 10], 6)
    ]
    
    for nums, target in test_cases:
        result = binary_search_left(nums, target)
        is_valid = verify_first_occurrence(nums, target, result)
        print(f"nums={nums}, target={target}, first_index={result}, valid={is_valid}")
