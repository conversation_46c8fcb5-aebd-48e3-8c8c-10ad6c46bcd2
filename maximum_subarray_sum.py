def maximum_subarray_sum(nums):
    """
    Find the maximum sum of any contiguous subarray using <PERSON><PERSON><PERSON>'s Algorithm.

    Time Complexity: O(n)
    Space Complexity: O(1)

    Args:
        nums: List of integers (can contain negative numbers)

    Returns:
        Maximum sum of any contiguous subarray

    Example:
        maximum_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 4]) = 6
        The subarray [4, -1, 2, 1] has the maximum sum of 6
    """
    if not nums:
        return 0

    max_sum = float('-inf')  # Handle all-negative arrays
    current_sum = 0

    for num in nums:
        # Either start new subarray at current element or extend existing
        current_sum = max(num, current_sum + num)
        # Update maximum sum seen so far
        max_sum = max(max_sum, current_sum)

    return max_sum


print(maximum_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 4]))