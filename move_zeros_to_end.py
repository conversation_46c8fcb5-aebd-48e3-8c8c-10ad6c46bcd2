def move_zeros_to_end(nums):
    """
    Move all zeros in the list to the end while maintaining the order of non-zero elements.

    Time Complexity: O(n)
    Space Complexity: O(1)
    """
    non_zero_index = 0
    for i in range(len(nums)):
        if nums[i] != 0:
            nums[non_zero_index] = nums[i]
            non_zero_index += 1

    for i in range(non_zero_index, len(nums)):
        nums[i] = 0

    return nums


print(move_zeros_to_end([0, 1, 0, 3, 12]))