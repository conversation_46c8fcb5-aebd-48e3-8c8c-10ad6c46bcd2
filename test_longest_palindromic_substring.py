import time
from longest_palindromic_substring import longest_palindromic_substring

def test_correctness():
    """Test the correctness of the optimized function."""
    test_cases = [
        ("", ""),
        ("a", "a"),
        ("ab", "a"),  # or "b", both are valid
        ("aba", "aba"),
        ("abcd", "a"),  # or any single character
        ("racecar", "racecar"),
        ("babad", "bab"),  # or "aba", both are valid
        ("cbbd", "bb"),
        ("abcdef", "a"),  # or any single character
        ("noon", "noon"),
        ("abacabad", "abacaba"),
        ("forgeeksskeegfor", "geeksskeeg"),
        ("abcdcba", "abcdcba"),
        ("aabbaa", "aabbaa"),
    ]
    
    print("Testing correctness...")
    for i, (input_str, expected) in enumerate(test_cases):
        result = longest_palindromic_substring(input_str)
        # For cases with multiple valid answers, check if result is a palindrome of expected length
        if len(result) == len(expected) and result == result[::-1]:
            print(f"✓ Test {i+1}: '{input_str}' -> '{result}' (length: {len(result)})")
        elif result == expected:
            print(f"✓ Test {i+1}: '{input_str}' -> '{result}'")
        else:
            print(f"✗ Test {i+1}: '{input_str}' -> '{result}' (expected length: {len(expected)})")

def test_performance():
    """Test the performance of the optimized function."""
    print("\nTesting performance...")
    
    # Test with different string sizes
    test_strings = [
        "a" * 100,
        "ab" * 100,  # No long palindromes
        "a" * 50 + "b" + "a" * 50,  # Palindrome in middle
        "racecar" * 50,  # Multiple palindromes
        "abcdefghijklmnopqrstuvwxyz" * 20,  # No palindromes longer than 1
    ]
    
    for i, test_str in enumerate(test_strings):
        start_time = time.time()
        result = longest_palindromic_substring(test_str)
        end_time = time.time()
        
        print(f"Test {i+1}: String length {len(test_str)}, "
              f"Result length {len(result)}, "
              f"Time: {(end_time - start_time)*1000:.2f}ms")

if __name__ == "__main__":
    test_correctness()
    test_performance()
