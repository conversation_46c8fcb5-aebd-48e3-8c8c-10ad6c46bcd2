# Import the modified functions from the original file
import sys
sys.path.append('.')

# Copy the functions to test them
def binary_search_left(nums, target):
    """Modified binary_search_left that returns -1 if target doesn't exist."""
    if not nums:
        return -1
    
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:
            right = mid - 1
        else:
            left = mid + 1
    
    # Check if target actually exists at the found position
    if left < len(nums) and nums[left] == target:
        return left
    else:
        return -1

def binary_search_right(nums, target):
    """Original binary_search_right function."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target >= nums[mid]:
            left = mid + 1
        else:
            right = mid - 1
    return right

def count_occurrences_of_k_in_sorted_array(nums, k):
    """Updated count function using modified binary_search_left."""
    left_idx = binary_search_left(nums, k)
    if left_idx == -1:  # Target doesn't exist
        return 0
    
    right_idx = binary_search_right(nums, k)
    return right_idx - left_idx + 1

def test_complete_implementation():
    """Test the complete implementation with modified binary_search_left."""
    
    test_cases = [
        # (array, target, expected_count, description)
        ([1, 2, 3, 4, 4, 4, 5, 6], 4, 3, "Multiple occurrences"),
        ([1, 2, 3, 4, 4, 4, 5, 6], 1, 1, "Single occurrence at start"),
        ([1, 2, 3, 4, 4, 4, 5, 6], 6, 1, "Single occurrence at end"),
        ([1, 2, 3, 4, 4, 4, 5, 6], 3, 1, "Single occurrence in middle"),
        ([1, 2, 3, 4, 4, 4, 5, 6], 7, 0, "Target not in array (larger)"),
        ([1, 2, 3, 4, 4, 4, 5, 6], 0, 0, "Target not in array (smaller)"),
        ([1, 1, 1, 1, 1], 1, 5, "All elements same"),
        ([5], 5, 1, "Single element - found"),
        ([5], 3, 0, "Single element - not found"),
        ([], 1, 0, "Empty array"),
        ([1, 2, 2, 2, 2, 3], 2, 4, "Many duplicates"),
        ([1, 3, 5, 7, 9], 4, 0, "Target between elements"),
    ]
    
    print("TESTING COMPLETE IMPLEMENTATION")
    print("=" * 50)
    print("Modified binary_search_left returns -1 if target doesn't exist")
    print("=" * 50)
    
    all_passed = True
    
    for i, (nums, target, expected, description) in enumerate(test_cases, 1):
        # Test individual functions
        left_result = binary_search_left(nums, target)
        right_result = binary_search_right(nums, target) if left_result != -1 else -1
        count_result = count_occurrences_of_k_in_sorted_array(nums, target)
        
        # Verify with manual count
        manual_count = nums.count(target)
        
        passed = count_result == expected == manual_count
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i:2d}: {status} - {description}")
        print(f"         Array: {nums}")
        print(f"         Target: {target}")
        print(f"         binary_search_left: {left_result}")
        print(f"         binary_search_right: {right_result}")
        print(f"         Count result: {count_result}")
        print(f"         Expected: {expected}")
        print(f"         Manual count: {manual_count}")
        
        if not passed:
            print(f"         ❌ MISMATCH!")
        
        print()
    
    print(f"OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def demonstrate_improvement():
    """Demonstrate the improvement made to binary_search_left."""
    
    print("\nDEMONSTRATING THE IMPROVEMENT")
    print("=" * 40)
    
    def binary_search_left_original(nums, target):
        """Original version that returns insertion point."""
        left, right = 0, len(nums) - 1
        while left <= right:
            mid = (left + right) // 2
            if target <= nums[mid]:
                right = mid - 1
            else:
                left = mid + 1
        return left
    
    test_cases = [
        ([1, 3, 5, 7, 9], 5),    # Exists
        ([1, 3, 5, 7, 9], 4),    # Doesn't exist
        ([1, 2, 2, 2, 3], 2),    # Multiple occurrences
        ([], 1),                 # Empty array
    ]
    
    for nums, target in test_cases:
        original = binary_search_left_original(nums, target)
        modified = binary_search_left(nums, target)
        exists = target in nums if nums else False
        
        print(f"Array: {nums}, Target: {target}")
        print(f"  Target exists: {exists}")
        print(f"  Original result: {original} ({'insertion point' if not exists else 'first occurrence'})")
        print(f"  Modified result: {modified} ({'first occurrence' if exists else 'not found (-1)'})")
        print(f"  Improvement: {'Returns -1 for non-existent targets' if not exists and modified == -1 else 'Same behavior for existing targets'}")
        print()

def verify_edge_cases():
    """Verify edge cases work correctly."""
    
    print("\nEDGE CASE VERIFICATION")
    print("=" * 30)
    
    # Edge case 1: Target at array boundaries
    nums1 = [1, 2, 3, 4, 5]
    for target in [0, 1, 5, 6]:
        result = binary_search_left(nums1, target)
        exists = target in nums1
        expected = nums1.index(target) if exists else -1
        correct = result == expected
        
        print(f"Array: {nums1}, Target: {target}")
        print(f"  Result: {result}, Expected: {expected}, Correct: {correct}")
    
    print()
    
    # Edge case 2: All duplicates
    nums2 = [3, 3, 3, 3, 3]
    result2 = binary_search_left(nums2, 3)
    print(f"All duplicates: {nums2}, Target: 3")
    print(f"  Result: {result2}, Should be 0: {result2 == 0}")
    
    # Edge case 3: Single element
    for nums, target in [([5], 5), ([5], 3)]:
        result = binary_search_left(nums, target)
        expected = 0 if target == 5 else -1
        print(f"Single element: {nums}, Target: {target}")
        print(f"  Result: {result}, Expected: {expected}, Correct: {result == expected}")

if __name__ == "__main__":
    test_complete_implementation()
    demonstrate_improvement()
    verify_edge_cases()
