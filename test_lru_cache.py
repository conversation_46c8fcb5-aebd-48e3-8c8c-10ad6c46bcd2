#!/usr/bin/env python3
"""
Test script for the updated LRUCache implementation.
"""

from lru_cache import LRUCache, LRUCacheOrderedDict


def test_lru_cache(cache_class, name):
    """Test LRU cache implementation."""
    print(f"\n=== Testing {name} ===")
    
    # Test with capacity 3
    cache = cache_class(3)
    
    # Test basic put and get
    cache.put(1, 10)
    cache.put(2, 20)
    cache.put(3, 30)
    
    assert cache.get(1) == 10, "Should get value 10 for key 1"
    assert cache.get(2) == 20, "Should get value 20 for key 2"
    assert cache.get(3) == 30, "Should get value 30 for key 3"
    print("✓ Basic put/get operations work")
    
    # Test LRU eviction
    cache.put(4, 40)  # Should evict key 1 (least recently used)
    assert cache.get(1) == -1, "Key 1 should be evicted"
    assert cache.get(4) == 40, "Key 4 should exist"
    print("✓ LRU eviction works")
    
    # Test updating existing key
    cache.put(2, 25)  # Update key 2
    assert cache.get(2) == 25, "Key 2 should have updated value"
    print("✓ Updating existing key works")
    
    # Test access order (get should move to front)
    cache.get(3)  # Access key 3, making it most recently used
    cache.put(5, 50)  # Should evict key 4 (now LRU), not key 3
    assert cache.get(3) == 30, "Key 3 should still exist (was accessed)"
    assert cache.get(4) == -1, "Key 4 should be evicted"
    assert cache.get(5) == 50, "Key 5 should exist"
    print("✓ Access order (MRU) works correctly")
    
    # Test edge cases
    cache_small = cache_class(1)
    cache_small.put(1, 100)
    cache_small.put(2, 200)  # Should evict key 1
    assert cache_small.get(1) == -1, "Key 1 should be evicted in capacity-1 cache"
    assert cache_small.get(2) == 200, "Key 2 should exist in capacity-1 cache"
    print("✓ Edge case (capacity=1) works")
    
    print(f"✅ All tests passed for {name}!")


def performance_comparison():
    """Simple performance comparison between implementations."""
    import time
    
    print("\n=== Performance Comparison ===")
    
    # Test with larger dataset
    n = 1000
    capacity = 100
    
    # Test doubly linked list implementation
    start_time = time.time()
    cache1 = LRUCache(capacity)
    for i in range(n):
        cache1.put(i, i * 10)
        if i % 2 == 0:
            cache1.get(i // 2)  # Access some keys to test move-to-front
    dll_time = time.time() - start_time
    
    # Test OrderedDict implementation
    start_time = time.time()
    cache2 = LRUCacheOrderedDict(capacity)
    for i in range(n):
        cache2.put(i, i * 10)
        if i % 2 == 0:
            cache2.get(i // 2)  # Access some keys to test move-to-front
    od_time = time.time() - start_time
    
    print(f"Doubly Linked List: {dll_time:.4f} seconds")
    print(f"OrderedDict:        {od_time:.4f} seconds")
    print(f"Ratio (DLL/OD):     {dll_time/od_time:.2f}x")


if __name__ == "__main__":
    # Test both implementations
    test_lru_cache(LRUCache, "LRUCache (Doubly Linked List)")
    test_lru_cache(LRUCacheOrderedDict, "LRUCacheOrderedDict")
    
    # Performance comparison
    performance_comparison()
    
    print("\n🎉 All tests completed successfully!")
