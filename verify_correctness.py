from longest_palindromic_substring import longest_palindromic_substring

def is_palindrome(s):
    """Helper function to verify if a string is a palindrome."""
    return s == s[::-1]

def verify_correctness():
    """Comprehensive verification of the longest_palindromic_substring function."""
    
    test_cases = [
        # Basic cases
        ("", ""),
        ("a", "a"),
        ("ab", "a"),  # Single char palindromes
        
        # Simple palindromes
        ("aba", "aba"),
        ("abba", "abba"),
        ("racecar", "racecar"),
        
        # Mixed cases
        ("babad", ["bab", "aba"]),  # Multiple valid answers
        ("cbbd", "bb"),
        ("abcdef", "a"),  # No palindrome longer than 1
        
        # Edge cases
        ("aa", "aa"),
        ("aaa", "aaa"),
        ("aaaa", "aaaa"),
        
        # Complex cases
        ("abacabad", "abacaba"),
        ("forgeeksskeegfor", "geeksskeeg"),
        ("noon", "noon"),
        ("level", "level"),
        
        # Cases with multiple palindromes
        ("abccba", "abccba"),
        ("abacabad", "abacaba"),
        ("tattarrattat", "tattarrattat"),  # Entire string is palindrome
        
        # Long strings
        ("a" * 100, "a" * 100),
        ("ab" * 50, "a"),  # No long palindromes
    ]
    
    print("Verifying correctness of longest_palindromic_substring...")
    print("=" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases):
        if len(test_case) == 2:
            input_str, expected = test_case
            expected_options = [expected] if isinstance(expected, str) else expected
        else:
            input_str, expected_options = test_case
        
        result = longest_palindromic_substring(input_str)
        
        # Verify the result is a palindrome
        if not is_palindrome(result):
            print(f"❌ Test {i+1}: Result '{result}' is not a palindrome!")
            all_passed = False
            continue
        
        # Check if result matches any expected option
        valid_result = False
        for expected in expected_options:
            if len(result) == len(expected):
                valid_result = True
                break
        
        if valid_result:
            display_input = input_str if len(input_str) <= 20 else f"{input_str[:17]}..."
            print(f"✅ Test {i+1}: '{display_input}' -> '{result}' (length: {len(result)})")
        else:
            expected_lengths = [len(exp) for exp in expected_options]
            print(f"❌ Test {i+1}: '{input_str}' -> '{result}' (length: {len(result)}, expected length: {expected_lengths})")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The function is working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all_passed

def verify_edge_cases():
    """Test specific edge cases that might cause issues."""
    print("\nTesting edge cases...")
    print("-" * 30)
    
    edge_cases = [
        ("Single character", "x"),
        ("Two same chars", "aa"),
        ("Two different chars", "xy"),
        ("All same chars", "aaaaa"),
        ("Palindrome at start", "abcdefg"),
        ("Palindrome at end", "xyzracecar"),
        ("Nested palindromes", "abacabad"),
        ("Very long palindrome", "a" * 500 + "b" + "a" * 500),
    ]
    
    for name, test_str in edge_cases:
        result = longest_palindromic_substring(test_str)
        is_valid = is_palindrome(result)
        
        if is_valid:
            print(f"✅ {name}: Found palindrome of length {len(result)}")
        else:
            print(f"❌ {name}: Result '{result}' is not a palindrome!")

if __name__ == "__main__":
    verify_correctness()
    verify_edge_cases()
