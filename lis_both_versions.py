"""
Longest Increasing Subsequence - Both O(n²) and O(n log n) implementations
"""

import bisect

def longest_increasing_subsequence_dp(nums):
    """
    O(n²) Dynamic Programming approach.
    
    Time Complexity: O(n²)
    Space Complexity: O(n)
    
    Algorithm:
    - dp[i] = length of LIS ending at index i
    - For each position i, check all previous positions j < i
    - If nums[i] > nums[j], we can extend the LIS ending at j
    """
    if not nums:
        return 0
    
    dp = [1] * len(nums)
    
    for i in range(1, len(nums)):
        for j in range(i):
            if nums[i] > nums[j]:
                dp[i] = max(dp[i], dp[j] + 1)
    
    return max(dp)

def longest_increasing_subsequence_binary_search(nums):
    """
    O(n log n) Binary Search approach (Patience Sorting).
    
    Time Complexity: O(n log n)
    Space Complexity: O(n)
    
    Algorithm:
    - Maintain 'tails' array where tails[i] = smallest ending element
      of all increasing subsequences of length i+1
    - For each number, use binary search to find its position
    - Either extend the sequence or replace for better future options
    """
    if not nums:
        return 0
    
    tails = []
    
    for num in nums:
        # Binary search to find position
        pos = bisect.bisect_left(tails, num)
        
        if pos == len(tails):
            # Extend the sequence
            tails.append(num)
        else:
            # Replace for better ending element
            tails[pos] = num
    
    return len(tails)

def longest_increasing_subsequence_with_sequence(nums):
    """
    O(n log n) approach that also returns the actual LIS.
    
    Returns: (length, actual_subsequence)
    """
    if not nums:
        return 0, []
    
    tails = []
    predecessors = [-1] * len(nums)
    tail_indices = []
    
    for i, num in enumerate(nums):
        pos = bisect.bisect_left(tails, num)
        
        if pos == len(tails):
            tails.append(num)
            tail_indices.append(i)
        else:
            tails[pos] = num
            tail_indices[pos] = i
        
        # Track predecessor for reconstruction
        if pos > 0:
            predecessors[i] = tail_indices[pos - 1]
    
    # Reconstruct the actual LIS
    lis_length = len(tails)
    if lis_length == 0:
        return 0, []
    
    lis = []
    current = tail_indices[-1]
    
    while current != -1:
        lis.append(nums[current])
        current = predecessors[current]
    
    lis.reverse()
    return lis_length, lis

def demonstrate_algorithm():
    """Demonstrate both algorithms with step-by-step execution."""
    
    test_input = [10, 9, 2, 5, 3, 7, 101, 18]
    
    print("LONGEST INCREASING SUBSEQUENCE DEMONSTRATION")
    print("=" * 50)
    print(f"Input: {test_input}")
    print()
    
    # Test O(n²) approach
    print("O(n²) Dynamic Programming Approach:")
    print("-" * 35)
    result_dp = longest_increasing_subsequence_dp(test_input)
    print(f"Result: {result_dp}")
    print()
    
    # Test O(n log n) approach
    print("O(n log n) Binary Search Approach:")
    print("-" * 35)
    result_bs = longest_increasing_subsequence_binary_search(test_input)
    print(f"Result: {result_bs}")
    print()
    
    # Test with actual sequence
    print("O(n log n) with Actual Subsequence:")
    print("-" * 35)
    length, sequence = longest_increasing_subsequence_with_sequence(test_input)
    print(f"Length: {length}")
    print(f"Actual LIS: {sequence}")
    print()
    
    # Verify results match
    print(f"Results match: {result_dp == result_bs == length} ✅")

def performance_test():
    """Test performance difference between approaches."""
    
    import time
    import random
    
    print("\nPERFORMANCE COMPARISON")
    print("=" * 30)
    
    sizes = [100, 500, 1000, 2000]
    
    print(f"{'Size':<8} {'O(n²) Time':<12} {'O(n log n) Time':<16} {'Speedup':<10}")
    print("-" * 55)
    
    for size in sizes:
        # Generate random test data
        nums = [random.randint(1, 1000) for _ in range(size)]
        
        # Test O(n²) approach
        start_time = time.time()
        result_dp = longest_increasing_subsequence_dp(nums)
        time_dp = time.time() - start_time
        
        # Test O(n log n) approach
        start_time = time.time()
        result_bs = longest_increasing_subsequence_binary_search(nums)
        time_bs = time.time() - start_time
        
        speedup = time_dp / time_bs if time_bs > 0 else float('inf')
        
        print(f"{size:<8} {time_dp:<12.4f} {time_bs:<16.4f} {speedup:<10.1f}x")

def test_edge_cases():
    """Test both implementations with edge cases."""
    
    print("\nEDGE CASES TEST")
    print("=" * 20)
    
    test_cases = [
        [],
        [1],
        [1, 2, 3, 4, 5],
        [5, 4, 3, 2, 1],
        [1, 1, 1, 1],
        [1, 3, 2, 4],
        [10, 9, 2, 5, 3, 7, 101, 18],
    ]
    
    print(f"{'Input':<25} {'O(n²)':<8} {'O(n log n)':<12} {'Match'}")
    print("-" * 50)
    
    for nums in test_cases:
        result_dp = longest_increasing_subsequence_dp(nums)
        result_bs = longest_increasing_subsequence_binary_search(nums)
        match = "✅" if result_dp == result_bs else "❌"
        
        nums_str = str(nums) if len(nums) <= 8 else f"{nums[:3]}...+{len(nums)-3}"
        print(f"{nums_str:<25} {result_dp:<8} {result_bs:<12} {match}")

def algorithm_summary():
    """Provide a summary of when to use each algorithm."""
    
    print("\nALGORITHM SELECTION GUIDE")
    print("=" * 30)
    print("""
O(n²) DYNAMIC PROGRAMMING:
✅ Use when: Input size < 1,000
✅ Pros: Simple, easy to understand and modify
✅ Cons: Slow for large inputs
✅ Best for: Educational purposes, small datasets

O(n log n) BINARY SEARCH:
✅ Use when: Input size ≥ 1,000 or performance is critical
✅ Pros: Much faster, scales well
✅ Cons: More complex, harder to modify
✅ Best for: Production systems, competitive programming

KEY INSIGHT:
The O(n log n) algorithm doesn't track all possible subsequences.
Instead, it maintains the "best" (smallest) ending element for each
possible subsequence length, using binary search for efficiency.
""")

if __name__ == "__main__":
    demonstrate_algorithm()
    performance_test()
    test_edge_cases()
    algorithm_summary()
