def course_order(courses):
    """
    Find a valid ordering of courses given prerequisites using topological sort.

    Args:
        courses: List of [course, prerequisite] pairs where each course and
                prerequisite is represented by an integer index.

    Returns:
        List of course indices in valid order, or empty list if impossible
        (due to circular dependencies).

    Time Complexity: O(V + E) where V is number of courses, E is number of prerequisites
    Space Complexity: O(V + E) for graph and auxiliary data structures

    Example:
        courses = [[1, 0], [2, 0], [3, 1], [3, 2]]
        # Course 1 requires 0, Course 2 requires 0, Course 3 requires 1 and 2
        # Valid order: [0, 1, 2, 3] or [0, 2, 1, 3]
    """
    if not courses:
        return []

    # Determine number of courses from the input
    num_courses = max(max(course, prereq) for course, prereq in courses) + 1

    # Build adjacency list and in-degree array
    graph = {i: [] for i in range(num_courses)}
    in_degree = [0] * num_courses

    for course, prereq in courses:
        graph[prereq].append(course)
        in_degree[course] += 1

    # Find all courses with no prerequisites (in-degree 0)
    queue = [i for i in range(num_courses) if in_degree[i] == 0]
    order = []

    # Process courses in topological order
    while queue:
        course = queue.pop(0)
        order.append(course)

        # Remove this course and update in-degrees of dependent courses
        for next_course in graph[course]:
            in_degree[next_course] -= 1
            if in_degree[next_course] == 0:
                queue.append(next_course)

    # Return valid order if all courses processed, empty list if cycle detected
    return order if len(order) == num_courses else []


# Unit Tests
def test_course_order():
    """Comprehensive unit tests for course_order function."""

    def test_case(name, courses, expected_valid=True, expected_length=None):
        """Helper function to run a test case."""
        print(f"\n--- Testing: {name} ---")
        print(f"Input: {courses}")

        result = course_order(courses)
        print(f"Output: {result}")

        if expected_valid:
            if expected_length is not None:
                assert len(result) == expected_length, f"Expected length {expected_length}, got {len(result)}"
            else:
                assert len(result) > 0, f"Expected valid order, got empty list"

            # Verify the ordering is valid
            if courses:
                position = {course: i for i, course in enumerate(result)}
                for course, prereq in courses:
                    assert position[prereq] < position[course], \
                        f"Prerequisite {prereq} should come before course {course}"
            print("✓ Valid ordering confirmed")
        else:
            assert len(result) == 0, f"Expected empty list (cycle), got {result}"
            print("✓ Correctly detected cycle")

    # Test 1: Empty input
    test_case("Empty courses", [], expected_valid=True, expected_length=0)

    # Test 2: Simple linear chain
    test_case("Linear chain", [[1, 0], [2, 1], [3, 2]], expected_valid=True, expected_length=4)

    # Test 3: Multiple valid orderings
    test_case("Multiple valid paths", [[1, 0], [2, 0], [3, 1], [3, 2]], expected_valid=True, expected_length=4)

    # Test 4: Single course with prerequisite
    test_case("Single dependency", [[1, 0]], expected_valid=True, expected_length=2)

    # Test 5: Independent courses (no dependencies between them)
    test_case("Some independent courses", [[1, 0], [3, 2]], expected_valid=True, expected_length=4)

    # Test 6: Cycle detection - simple cycle
    test_case("Simple cycle", [[1, 0], [0, 1]], expected_valid=False)

    # Test 7: Cycle detection - complex cycle
    test_case("Complex cycle", [[1, 0], [2, 1], [3, 2], [0, 3]], expected_valid=False)

    # Test 8: Larger valid graph
    test_case("Larger valid graph",
              [[1, 0], [2, 0], [3, 1], [4, 1], [5, 2], [5, 3]],
              expected_valid=True, expected_length=6)

    # Test 9: Self-loop (course depends on itself)
    test_case("Self-loop", [[0, 0]], expected_valid=False)

    # Test 10: Mixed scenario with some cycles
    test_case("Partial cycle", [[1, 0], [2, 1], [1, 2], [3, 0]], expected_valid=False)

    print("\n🎉 All test cases passed!")


if __name__ == "__main__":
    test_course_order()