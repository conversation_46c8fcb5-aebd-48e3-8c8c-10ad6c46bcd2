from datetime import datetime, timedelta
from collections import deque
import bisect
from typing import List, Tu<PERSON>, Set

class SocialEventsTracker:
    """
    Tracks social events and counts unique users in sliding time windows.
    Supports both real-time streaming and batch processing.
    """
    
    def __init__(self, window_minutes: int = 10):
        self.window_minutes = window_minutes
        self.window_duration = timedelta(minutes=window_minutes)
        
        # For streaming approach - maintain events in chronological order
        self.events = deque()  # (timestamp, user_id)
        
    def add_event(self, user_id: str, timestamp: str, event_type: str) -> int:
        """
        Add a new event and return count of unique users in last window.
        
        Args:
            user_id: User identifier
            timestamp: ISO format timestamp string
            event_type: Type of event (comment, like, share, etc.)
            
        Returns:
            Number of unique users who engaged in the last window
        """
        # Parse timestamp
        event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Add new event
        self.events.append((event_time, user_id))
        
        # Remove events outside the window
        self._cleanup_old_events(event_time)
        
        # Count unique users
        return self._count_unique_users()
    
    def _cleanup_old_events(self, current_time: datetime):
        """Remove events older than the sliding window."""
        cutoff_time = current_time - self.window_duration
        
        while self.events and self.events[0][0] < cutoff_time:
            self.events.popleft()
    
    def _count_unique_users(self) -> int:
        """Count unique users in current window."""
        unique_users = set()
        for _, user_id in self.events:
            unique_users.add(user_id)
        return len(unique_users)
    
    def get_unique_users_at_time(self, query_time: str) -> int:
        """
        Get count of unique users in the 10-minute window ending at query_time.
        Useful for historical analysis.
        """
        query_datetime = datetime.fromisoformat(query_time.replace('Z', '+00:00'))
        cutoff_time = query_datetime - self.window_duration
        
        unique_users = set()
        for event_time, user_id in self.events:
            if cutoff_time <= event_time <= query_datetime:
                unique_users.add(user_id)
        
        return len(unique_users)


class BatchSocialEventsAnalyzer:
    """
    Analyzes a batch of social events to find unique users in sliding windows.
    Optimized for processing large datasets.
    """
    
    def __init__(self, window_minutes: int = 10):
        self.window_minutes = window_minutes
        self.window_duration = timedelta(minutes=window_minutes)
    
    def analyze_events(self, events: List[Tuple[str, str, str]]) -> List[Tuple[str, int]]:
        """
        Analyze a list of events and return unique user counts for each timestamp.
        
        Args:
            events: List of (user_id, timestamp, event_type) tuples
            
        Returns:
            List of (timestamp, unique_user_count) tuples
        """
        # Parse and sort events by timestamp
        parsed_events = []
        for user_id, timestamp, event_type in events:
            event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            parsed_events.append((event_time, user_id, event_type))
        
        parsed_events.sort(key=lambda x: x[0])  # Sort by timestamp
        
        results = []
        
        for i, (current_time, _, _) in enumerate(parsed_events):
            # Find events in the sliding window ending at current_time
            cutoff_time = current_time - self.window_duration
            
            # Use binary search to find the start of the window
            start_idx = self._find_window_start(parsed_events, cutoff_time, i)
            
            # Count unique users in the window
            unique_users = set()
            for j in range(start_idx, i + 1):
                unique_users.add(parsed_events[j][1])  # user_id
            
            results.append((current_time.isoformat(), len(unique_users)))
        
        return results
    
    def _find_window_start(self, events: List[Tuple], cutoff_time: datetime, end_idx: int) -> int:
        """Find the first event within the sliding window using binary search."""
        left, right = 0, end_idx
        
        while left <= right:
            mid = (left + right) // 2
            if events[mid][0] >= cutoff_time:
                right = mid - 1
            else:
                left = mid + 1
        
        return left
    
    def get_unique_users_in_timerange(self, events: List[Tuple[str, str, str]], 
                                    start_time: str, end_time: str) -> int:
        """
        Count unique users who engaged between start_time and end_time.
        """
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        unique_users = set()
        for user_id, timestamp, _ in events:
            event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            if start_dt <= event_time <= end_dt:
                unique_users.add(user_id)
        
        return len(unique_users)


def simple_sliding_window_count(events: List[Tuple[str, str, str]], 
                               query_time: str, 
                               window_minutes: int = 10) -> int:
    """
    Simple function to count unique users in sliding window.
    
    Args:
        events: List of (user_id, timestamp, event_type) tuples
        query_time: Time to query (end of sliding window)
        window_minutes: Size of sliding window in minutes
        
    Returns:
        Number of unique users in the sliding window
    """
    query_datetime = datetime.fromisoformat(query_time.replace('Z', '+00:00'))
    cutoff_time = query_datetime - timedelta(minutes=window_minutes)
    
    unique_users = set()
    
    for user_id, timestamp, event_type in events:
        event_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Check if event is within the sliding window
        if cutoff_time <= event_time <= query_datetime:
            unique_users.add(user_id)
    
    return len(unique_users)


# Example usage and test functions
def test_social_events_tracker():
    """Test the streaming social events tracker."""
    print("Testing SocialEventsTracker (Streaming):")
    print("=" * 50)
    
    tracker = SocialEventsTracker(window_minutes=10)
    
    # Sample events
    events = [
        ("u123", "2025-10-13T19:00:00", "comment"),
        ("u456", "2025-10-13T19:02:00", "like"),
        ("u123", "2025-10-13T19:03:00", "share"),  # Same user, shouldn't increase count
        ("u789", "2025-10-13T19:05:00", "comment"),
        ("u101", "2025-10-13T19:07:00", "like"),
        ("u456", "2025-10-13T19:12:00", "comment"),  # Outside 10-min window from first event
        ("u999", "2025-10-13T19:13:00", "share"),
    ]
    
    for user_id, timestamp, event_type in events:
        count = tracker.add_event(user_id, timestamp, event_type)
        print(f"Event: {user_id} {event_type} at {timestamp} -> Unique users: {count}")
    
    print()


def test_batch_analyzer():
    """Test the batch analyzer."""
    print("Testing BatchSocialEventsAnalyzer:")
    print("=" * 50)
    
    analyzer = BatchSocialEventsAnalyzer(window_minutes=10)
    
    events = [
        ("u123", "2025-10-13T19:00:00", "comment"),
        ("u456", "2025-10-13T19:02:00", "like"),
        ("u123", "2025-10-13T19:03:00", "share"),
        ("u789", "2025-10-13T19:05:00", "comment"),
        ("u101", "2025-10-13T19:07:00", "like"),
        ("u456", "2025-10-13T19:12:00", "comment"),
        ("u999", "2025-10-13T19:13:00", "share"),
    ]
    
    results = analyzer.analyze_events(events)
    
    for timestamp, count in results:
        print(f"At {timestamp}: {count} unique users in last 10 minutes")
    
    print()


def test_simple_function():
    """Test the simple sliding window function."""
    print("Testing simple_sliding_window_count:")
    print("=" * 50)
    
    events = [
        ("u123", "2025-10-13T19:00:00", "comment"),
        ("u456", "2025-10-13T19:02:00", "like"),
        ("u123", "2025-10-13T19:03:00", "share"),
        ("u789", "2025-10-13T19:05:00", "comment"),
        ("u101", "2025-10-13T19:07:00", "like"),
        ("u456", "2025-10-13T19:12:00", "comment"),
        ("u999", "2025-10-13T19:13:00", "share"),
    ]
    
    # Test at different query times
    query_times = [
        "2025-10-13T19:05:00",
        "2025-10-13T19:10:00", 
        "2025-10-13T19:15:00"
    ]
    
    for query_time in query_times:
        count = simple_sliding_window_count(events, query_time)
        print(f"Unique users in 10 minutes before {query_time}: {count}")
    
    print()


if __name__ == "__main__":
    test_social_events_tracker()
    test_batch_analyzer()
    test_simple_function()
