import bisect

def longest_increasing_subsequence_optimized(nums):
    """
    Optimized LIS using binary search - O(n log n)
    
    Algorithm: Patience Sorting / Binary Search approach
    - Maintain an array 'tails' where tails[i] is the smallest ending element
      of all increasing subsequences of length i+1
    - For each number, find the position to place it using binary search
    """
    if not nums:
        return 0
    
    tails = []
    
    for num in nums:
        # Find the position to insert/replace using binary search
        pos = bisect.bisect_left(tails, num)
        
        if pos == len(tails):
            # num is larger than all elements in tails, extend the sequence
            tails.append(num)
        else:
            # Replace the element at pos with num (smaller ending element)
            tails[pos] = num
    
    return len(tails)

def longest_increasing_subsequence_dp(nums):
    """
    Original DP approach - O(n²)
    """
    if not nums:
        return 0
    dp = [1] * len(nums)
    for i in range(1, len(nums)):
        for j in range(i):
            if nums[i] > nums[j]:
                dp[i] = max(dp[i], dp[j] + 1)
    return max(dp)

def compare_algorithms():
    """Compare both algorithms with examples."""
    test_cases = [
        [],
        [1],
        [1, 2, 3, 4, 5],
        [5, 4, 3, 2, 1],
        [10, 9, 2, 5, 3, 7, 101, 18],
        [0, 1, 0, 3, 2, 3],
        [7, 7, 7, 7, 7, 7, 7],
        [1, 3, 6, 7, 9, 4, 10, 5, 6],
    ]
    
    print("Comparing LIS Algorithms:")
    print("=" * 50)
    print(f"{'Input':<25} {'DP O(n²)':<10} {'Optimized O(n log n)':<20}")
    print("-" * 50)
    
    for nums in test_cases:
        dp_result = longest_increasing_subsequence_dp(nums)
        opt_result = longest_increasing_subsequence_optimized(nums)
        
        nums_str = str(nums) if len(nums) <= 8 else f"{nums[:5]}...+{len(nums)-5}"
        
        if dp_result == opt_result:
            print(f"{nums_str:<25} {dp_result:<10} {opt_result:<20} ✅")
        else:
            print(f"{nums_str:<25} {dp_result:<10} {opt_result:<20} ❌")

def explain_optimized_algorithm():
    """Explain how the optimized algorithm works."""
    print("\nOptimized Algorithm Explanation:")
    print("=" * 40)
    print("""
The O(n log n) algorithm uses the "Patience Sorting" concept:

1. Maintain array 'tails' where tails[i] = smallest ending element 
   of all increasing subsequences of length i+1

2. For each number in input:
   - Use binary search to find where it should be placed in 'tails'
   - If it's larger than all elements, append it (extend sequence)
   - Otherwise, replace the element at that position (better ending)

3. The length of 'tails' is the LIS length

Example: [10, 9, 2, 5, 3, 7, 101, 18]

Step by step:
- 10: tails = [10]
- 9:  tails = [9]     (replace 10 with 9, better ending for length 1)
- 2:  tails = [2]     (replace 9 with 2, even better ending)
- 5:  tails = [2, 5]  (extend, 5 > 2)
- 3:  tails = [2, 3]  (replace 5 with 3, better ending for length 2)
- 7:  tails = [2, 3, 7]  (extend, 7 > 3)
- 101: tails = [2, 3, 7, 101]  (extend, 101 > 7)
- 18: tails = [2, 3, 7, 18]    (replace 101 with 18, better ending)

Final length: 4
""")

def benchmark_performance():
    """Benchmark the performance difference."""
    import time
    import random
    
    print("Performance Benchmark:")
    print("=" * 30)
    
    sizes = [100, 500, 1000, 2000]
    
    for size in sizes:
        # Generate random test data
        nums = [random.randint(1, 1000) for _ in range(size)]
        
        # Test DP approach
        start_time = time.time()
        dp_result = longest_increasing_subsequence_dp(nums)
        dp_time = time.time() - start_time
        
        # Test optimized approach
        start_time = time.time()
        opt_result = longest_increasing_subsequence_optimized(nums)
        opt_time = time.time() - start_time
        
        speedup = dp_time / opt_time if opt_time > 0 else float('inf')
        
        print(f"Size {size:4d}: DP={dp_time:.4f}s, Opt={opt_time:.4f}s, "
              f"Speedup={speedup:.1f}x, Results match: {dp_result == opt_result}")

if __name__ == "__main__":
    # Test with the example from the original file
    test_input = [10, 9, 2, 5, 3, 7, 101, 18]
    
    print("Testing with [10, 9, 2, 5, 3, 7, 101, 18]:")
    print(f"DP approach (O(n²)): {longest_increasing_subsequence_dp(test_input)}")
    print(f"Optimized approach (O(n log n)): {longest_increasing_subsequence_optimized(test_input)}")
    print()
    
    compare_algorithms()
    explain_optimized_algorithm()
    benchmark_performance()
