def first_non_repeating_integer(arr):
    integer_frequency = {}
    non_repeating_queue = []
    for integer in arr:
        if integer not in integer_frequency:
            integer_frequency[integer] = 0
            non_repeating_queue.append(integer)
        integer_frequency[integer] += 1

    for integer in non_repeating_queue:
        if integer_frequency[integer] == 1:
            return integer
    return None


print(first_non_repeating_integer([1, 2, 3, 4, 5, 1, 2, 3, 4, 6]))