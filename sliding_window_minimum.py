from collections import deque

def sliding_window_minimum(nums, k):
    """
    Simpler O(n) solution using collections.deque.

    The deque stores indices in increasing order of their values.
    Front of deque always contains index of minimum element.
    """
    if not nums or k == 0:
        return []

    dq = deque()  # Store indices
    result = []

    for i in range(len(nums)):
        # Remove indices outside current window
        while dq and dq[0] <= i - k:
            dq.popleft()

        # Remove indices whose values are >= current value
        while dq and nums[dq[-1]] >= nums[i]:
            dq.pop()

        # Add current index
        dq.append(i)

        # Add minimum to result when window is complete
        if i >= k - 1:
            result.append(nums[dq[0]])

    return result


def sliding_window_minimum_original(nums, k):
    """Original implementation using list (for comparison)"""
    if not nums:
        return []
    if k == 0:
        return nums
    result = []
    window = []
    for i in range(len(nums)):
        if window and window[0] == i - k:
            window.pop(0)
        while window and nums[window[-1]] > nums[i]:
            window.pop()
        window.append(i)
        if i >= k - 1:
            result.append(nums[window[0]])
    return result


# Test both implementations
test_cases = [
    ([1, 3, -1, -3, 5, 3, 6, 7], 3),
    ([10, 20, 30, 40], 3),
    ([1], 1),
    ([7, 2, 4], 2)
]

for nums, k in test_cases:
    result1 = sliding_window_minimum(nums, k)
    result2 = sliding_window_minimum_original(nums, k)
    print(f"nums={nums}, k={k}")
    print(f"New:      {result1}")
    print(f"Original: {result2}")
    print(f"Match: {result1 == result2}")
    print()