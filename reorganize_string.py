from collections import Counter
import heapq

def reorganize_string(s):
    if not s:
        return ""
    if len(s) == 1:
        return s
    char_frequency = Counter(s)
    max_heap = []
    for char, frequency in char_frequency.items():
        heapq.heappush(max_heap, (-frequency, char))
    result = []
    while len(max_heap) > 1:
        freq1, char1 = heapq.heappop(max_heap)
        freq2, char2 = heapq.heappop(max_heap)
        result.append(char1)
        result.append(char2)
        if freq1 + 1 < 0:
            heapq.heappush(max_heap, (freq1 + 1, char1))
        if freq2 + 1 < 0:
            heapq.heappush(max_heap, (freq2 + 1, char2))
    if max_heap:
        freq, char = heapq.heappop(max_heap)
        if freq < -1:
            return ""
        result.append(char)
    return "".join(result)

print(reorganize_string("aab"))