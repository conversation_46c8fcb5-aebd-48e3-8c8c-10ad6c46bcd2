import heapq
def merge_k_sorted_arrays(arrays):
    heap = []
    result = []
    for array_index in range(len(arrays)):
        if arrays[array_index]:
            heapq.heappush(heap, (arrays[array_index][0], array_index, 0))
    while heap:
        val, array_index, element_index = heapq.heappop(heap)
        result.append(val)
        if element_index + 1 < len(arrays[array_index]):
            heapq.heappush(heap, (arrays[array_index][element_index + 1], array_index, element_index + 1))
    return result


result = merge_k_sorted_arrays([[1, 3, 5], [2, 4, 6], [7, 8, 9], []])
print(result)