from longest_palindromic_substring import longest_palindromic_substring

def comprehensive_verification():
    """Final comprehensive verification with corrected test cases."""
    
    test_cases = [
        # Basic cases
        ("", ""),
        ("a", "a"),
        ("ab", "a"),
        
        # Simple palindromes
        ("aba", "aba"),
        ("abba", "abba"),
        ("racecar", "racecar"),
        
        # Your original test cases
        ("babad", "bab"),  # Could also be "aba"
        ("cbbd", "bb"),
        
        # Edge cases
        ("aa", "aa"),
        ("aaa", "aaa"),
        ("abcdef", "a"),  # No palindrome longer than 1
        
        # Complex cases
        ("abacabad", "abacaba"),
        ("forgeeksskeegfor", "geeksskeeg"),
        ("noon", "noon"),
        ("level", "level"),
        ("tattarrattat", "tattarrattat"),
        
        # Pattern cases (corrected)
        ("abab", "aba"),  # 4 chars -> longest palindrome is "aba" (3 chars)
        ("ababab", "ababa"),  # 6 chars -> longest palindrome is "ababa" (5 chars)
        ("abababab", "abababa"),  # 8 chars -> longest palindrome is "abababa" (7 chars)
    ]
    
    print("Final Verification of longest_palindromic_substring")
    print("=" * 55)
    
    all_passed = True
    
    for i, (input_str, expected) in enumerate(test_cases):
        result = longest_palindromic_substring(input_str)
        
        # Check if result is a palindrome
        is_palindrome_result = result == result[::-1]
        
        # Check if length matches expected (allowing for multiple valid answers of same length)
        length_matches = len(result) == len(expected)
        
        if is_palindrome_result and length_matches:
            display_input = input_str if len(input_str) <= 15 else f"{input_str[:12]}..."
            display_result = result if len(result) <= 15 else f"{result[:12]}..."
            print(f"✅ Test {i+1:2d}: '{display_input}' -> '{display_result}' (len: {len(result)})")
        else:
            print(f"❌ Test {i+1:2d}: '{input_str}' -> '{result}'")
            if not is_palindrome_result:
                print(f"    Error: Result is not a palindrome!")
            if not length_matches:
                print(f"    Error: Expected length {len(expected)}, got {len(result)}")
            all_passed = False
    
    print("\n" + "=" * 55)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The longest_palindromic_substring function is CORRECT!")
    else:
        print("❌ Some tests failed.")
    
    return all_passed

def test_specific_examples():
    """Test the specific examples you ran."""
    print("\nTesting your specific examples:")
    print("-" * 35)
    
    examples = [
        "babad",
        "cbbd"
    ]
    
    for example in examples:
        result = longest_palindromic_substring(example)
        is_palindrome = result == result[::-1]
        print(f"Input: '{example}' -> Output: '{result}' (palindrome: {is_palindrome})")

if __name__ == "__main__":
    comprehensive_verification()
    test_specific_examples()
