def binary_search_left_correct(nums, target):
    """Find the leftmost (first) occurrence of target."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if nums[mid] >= target:  # Correct condition
            right = mid - 1
        else:
            left = mid + 1
    return left

def binary_search_right_correct(nums, target):
    """Find the rightmost (last) occurrence of target."""
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if nums[mid] <= target:  # Correct condition
            left = mid + 1
        else:
            right = mid - 1
    return right

def count_occurrences_correct(nums, k):
    """Correct implementation of count occurrences."""
    if not nums:
        return 0
    
    left_idx = binary_search_left_correct(nums, k)
    right_idx = binary_search_right_correct(nums, k)
    
    # Check if target actually exists
    if left_idx < len(nums) and nums[left_idx] == k:
        return right_idx - left_idx + 1
    else:
        return 0

# Original buggy implementation for comparison
def binary_search_left_buggy(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target <= nums[mid]:  # BUGGY: condition is backwards
            right = mid - 1
        else:
            left = mid + 1
    return left

def binary_search_right_buggy(nums, target):
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = (left + right) // 2
        if target >= nums[mid]:  # This part is actually correct
            left = mid + 1
        else:
            right = mid - 1
    return right

def count_occurrences_buggy(nums, k):
    left_idx = binary_search_left_buggy(nums, k)
    if left_idx == -1:  # BUGGY: will never be -1
        return 0
    right_idx = binary_search_right_buggy(nums, k)
    if right_idx == -1:  # BUGGY: will never be -1
        return 0
    return right_idx - left_idx + 1

# Test cases
test_cases = [
    ([1, 2, 3, 4, 4, 4, 5, 6], 4),  # Expected: 3
    ([1, 2, 3, 4, 4, 4, 5, 6], 7),  # Expected: 0 (not found)
    ([1, 1, 1, 1, 1], 1),           # Expected: 5 (all same)
    ([1, 2, 3, 4, 5], 3),           # Expected: 1 (single occurrence)
    ([], 1),                        # Expected: 0 (empty array)
    ([5], 5),                       # Expected: 1 (single element)
    ([5], 3),                       # Expected: 0 (single element, not found)
]

print("Testing count_occurrences implementations:")
print("=" * 60)

for i, (nums, k) in enumerate(test_cases, 1):
    correct_result = count_occurrences_correct(nums, k)
    buggy_result = count_occurrences_buggy(nums, k)
    
    # Manual count for verification
    manual_count = nums.count(k)
    
    print(f"Test {i}: nums={nums}, k={k}")
    print(f"  Manual count:     {manual_count}")
    print(f"  Correct function: {correct_result}")
    print(f"  Buggy function:   {buggy_result}")
    print(f"  Correct matches manual: {correct_result == manual_count}")
    print(f"  Buggy matches manual:   {buggy_result == manual_count}")
    print()

# Detailed analysis of the bug
print("DETAILED BUG ANALYSIS:")
print("=" * 60)

nums = [1, 2, 3, 4, 4, 4, 5, 6]
k = 4

print(f"Array: {nums}, Target: {k}")
print(f"Expected first occurrence at index: {nums.index(k)}")
print(f"Expected last occurrence at index: {len(nums) - 1 - nums[::-1].index(k)}")

left_correct = binary_search_left_correct(nums, k)
left_buggy = binary_search_left_buggy(nums, k)
right_correct = binary_search_right_correct(nums, k)
right_buggy = binary_search_right_buggy(nums, k)

print(f"\nCorrect binary_search_left: {left_correct}")
print(f"Buggy binary_search_left:   {left_buggy}")
print(f"Correct binary_search_right: {right_correct}")
print(f"Buggy binary_search_right:   {right_buggy}")

print(f"\nCorrect count: {right_correct - left_correct + 1}")
print(f"Buggy count:   {right_buggy - left_buggy + 1}")
