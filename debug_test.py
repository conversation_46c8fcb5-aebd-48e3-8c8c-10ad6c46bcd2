from longest_palindromic_substring import longest_palindromic_substring

def debug_ab_pattern():
    """Debug the 'ab' * 50 pattern to understand the result."""
    test_str = "ab" * 50  # Creates "abababab..."
    result = longest_palindromic_substring(test_str)
    
    print(f"Input string: '{test_str[:20]}...' (length: {len(test_str)})")
    print(f"Result: '{result[:20]}...' (length: {len(result)})")
    print(f"Is result a palindrome? {result == result[::-1]}")
    
    # Let's check what the actual longest palindrome should be
    # In "abababab...", the longest palindromes are odd-length centered palindromes
    # like "aba", "ababa", "abababa", etc.
    
    # Check if the result is actually correct
    if result == result[::-1]:
        print("✅ The result is indeed a palindrome!")
        
        # Let's see where this palindrome occurs in the original string
        start_pos = test_str.find(result)
        print(f"Palindrome found at position: {start_pos}")
        
        # Verify it's actually the longest
        print(f"Checking if there's a longer palindrome...")
        
        # The pattern "abab...ab" of length 99 would be:
        # Starting at position 0: "ababab...aba" (99 chars)
        # This should be a palindrome if it's odd length
        
        if len(result) == 99:
            print("This is likely the correct longest palindrome in the alternating pattern!")
    else:
        print("❌ Error: Result is not a palindrome!")

if __name__ == "__main__":
    debug_ab_pattern()
