from coin_change import coin_change

def brute_force_coin_change(coins, amount):
    """
    Brute force solution using recursion with memoization for verification.
    This will help us verify the correctness of the DP solution.
    """
    memo = {}
    
    def helper(remaining):
        if remaining == 0:
            return 0
        if remaining < 0:
            return float('inf')
        if remaining in memo:
            return memo[remaining]
        
        min_coins = float('inf')
        for coin in coins:
            result = helper(remaining - coin)
            if result != float('inf'):
                min_coins = min(min_coins, result + 1)
        
        memo[remaining] = min_coins
        return min_coins
    
    result = helper(amount)
    return result if result != float('inf') else -1

def verify_correctness():
    """Comprehensive verification of coin_change function."""
    
    test_cases = [
        # Basic cases
        ([1, 2, 5], 11, 3),  # 5+5+1 = 11
        ([2], 3, -1),        # Impossible
        ([1], 0, 0),         # Amount 0
        ([1], 1, 1),         # Single coin
        ([1], 2, 2),         # Multiple same coins
        
        # Edge cases
        ([], 1, -1),         # No coins
        ([1, 2, 5], 0, 0),   # Amount 0
        ([5], 3, -1),        # Impossible with larger coin
        
        # Standard cases
        ([1, 3, 4], 6, 2),   # 3+3 = 6
        ([2, 3, 5], 9, 3),   # 3+3+3 = 9
        ([1, 4, 5], 8, 2),   # 4+4 = 8
        
        # Cases with coin 1 (always possible)
        ([1, 5, 10, 25], 30, 2),  # 25+5 = 30
        ([1, 5, 10, 25], 67, 6),  # 25+25+10+5+1+1 = 67
        
        # Greedy doesn't work cases
        ([1, 3, 4], 6, 2),   # Greedy would do 4+1+1=3 coins, optimal is 3+3=2 coins
        ([1, 4, 5], 8, 2),   # Greedy would do 5+1+1+1=4 coins, optimal is 4+4=2 coins
        
        # Larger amounts
        ([1, 2, 5], 100, 20), # 20 coins of 5
        ([2, 5, 10], 18, 4),  # 10+5+2+1 is impossible, 5+5+5+3 is impossible, 10+2+2+2+2=5 coins
        
        # Single coin type
        ([5], 25, 5),        # 5*5 = 25
        ([3], 10, -1),       # Impossible
        
        # Large coin values
        ([186, 419, 83, 408], 6249, 20),  # Complex case
    ]
    
    print("Verifying correctness of coin_change...")
    print("=" * 60)
    
    all_passed = True
    
    for i, (coins, amount, expected) in enumerate(test_cases):
        # Test the DP solution
        result = coin_change(coins, amount)
        
        # For smaller cases, verify with brute force
        if amount <= 50 and len(coins) <= 10:
            brute_force_result = brute_force_coin_change(coins, amount)
            if result != brute_force_result:
                print(f"❌ Test {i+1:2d}: DP and brute force disagree!")
                print(f"    Coins: {coins}, Amount: {amount}")
                print(f"    DP result: {result}, Brute force: {brute_force_result}")
                all_passed = False
                continue
        
        if result == expected:
            coins_str = str(coins) if len(coins) <= 5 else f"{coins[:3]}...+{len(coins)-3}"
            print(f"✅ Test {i+1:2d}: coins={coins_str}, amount={amount} -> {result}")
        else:
            print(f"❌ Test {i+1:2d}: coins={coins}, amount={amount}")
            print(f"    Expected: {expected}, Got: {result}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The coin_change function is CORRECT!")
    else:
        print("❌ Some tests failed.")
    
    return all_passed

def test_edge_cases():
    """Test specific edge cases that might cause issues."""
    print("\nTesting edge cases...")
    print("-" * 40)
    
    edge_cases = [
        ("Empty coins", [], 5),
        ("Amount zero", [1, 2, 5], 0),
        ("Single coin exact", [5], 5),
        ("Single coin impossible", [5], 3),
        ("Large amount", [1, 2, 5], 1000),
        ("All same coins", [3, 3, 3], 9),
        ("Coins larger than amount", [10, 20], 5),
        ("Very large coins", [1000], 1),
    ]
    
    for name, coins, amount in edge_cases:
        result = coin_change(coins, amount)
        
        # Basic sanity checks
        if amount == 0 and result != 0:
            print(f"❌ {name}: Amount 0 should return 0, got {result}")
        elif not coins and amount > 0 and result != -1:
            print(f"❌ {name}: No coins with positive amount should return -1, got {result}")
        elif result == -1:
            print(f"✅ {name}: Correctly identified as impossible -> {result}")
        elif result >= 0:
            print(f"✅ {name}: Found solution with {result} coins")
        else:
            print(f"❌ {name}: Invalid result {result}")

def manual_verification():
    """Manually verify some specific cases to understand the algorithm."""
    print("\nManual verification of specific cases:")
    print("-" * 45)
    
    # Case 1: [1, 2, 5], 11
    print("Case 1: coins=[1, 2, 5], amount=11")
    print("Expected: 3 coins (5+5+1 or 5+2+2+2)")
    result1 = coin_change([1, 2, 5], 11)
    print(f"Result: {result1}\n")
    
    # Case 2: [2], 3
    print("Case 2: coins=[2], amount=3")
    print("Expected: -1 (impossible, can't make odd amount with even coins)")
    result2 = coin_change([2], 3)
    print(f"Result: {result2}\n")
    
    # Case 3: [1, 3, 4], 6
    print("Case 3: coins=[1, 3, 4], amount=6")
    print("Expected: 2 coins (3+3, not 4+1+1)")
    result3 = coin_change([1, 3, 4], 6)
    print(f"Result: {result3}\n")
    
    # Case 4: Edge case with amount 0
    print("Case 4: coins=[1, 2, 5], amount=0")
    print("Expected: 0 (no coins needed for amount 0)")
    result4 = coin_change([1, 2, 5], 0)
    print(f"Result: {result4}")

def algorithm_explanation():
    """Explain how the DP algorithm works."""
    print("\n" + "="*50)
    print("ALGORITHM EXPLANATION")
    print("="*50)
    print("""
The coin change algorithm uses Dynamic Programming:

1. State: dp[i] = minimum coins needed to make amount i
2. Base case: dp[0] = 0 (0 coins needed for amount 0)
3. Transition: For each amount i and each coin c:
   dp[i] = min(dp[i], dp[i-c] + 1) if i >= c

4. Initialize dp[i] = infinity for all i > 0
5. Return dp[amount] if possible, else -1

Example: coins=[1,2,5], amount=11
dp[0]=0, dp[1]=1, dp[2]=1, dp[3]=2, dp[4]=2, dp[5]=1,
dp[6]=2, dp[7]=2, dp[8]=3, dp[9]=3, dp[10]=2, dp[11]=3

Time Complexity: O(amount * len(coins))
Space Complexity: O(amount)
""")

if __name__ == "__main__":
    verify_correctness()
    test_edge_cases()
    manual_verification()
    algorithm_explanation()
