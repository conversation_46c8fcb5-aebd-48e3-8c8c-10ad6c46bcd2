"""
Sample Question:
There is one meeting room in a firm. There are N meetings in the form of (S[i], F[i]) where S[i] is
the start time of meeting i and F[i] is finish time of meeting i. The task is to find the maximum
number of meetings that can be accommodated in the meeting room. Print all meeting numbers

Input : s[] = {1, 3, 0, 5, 8, 5}, f[] = {2, 4, 6, 7, 9, 9}
Output : 1 2 4 5
First meeting [1, 2]
Second meeting [3, 4]
Fourth meeting [5, 7]
Fifth meeting [8, 9]
"""


def meeting_room_scheduler(start_times, finish_times):
    """
    Solve the Activity Selection Problem using greedy algorithm.

    Strategy: Always pick the meeting that finishes earliest and doesn't conflict
    with previously selected meetings.

    Args:
        start_times: List of meeting start times
        finish_times: List of meeting finish times

    Returns:
        List of meeting indices (1-based) that can be scheduled

    Time Complexity: O(n log n) due to sorting
    Space Complexity: O(n) for storing meeting data
    """
    if not start_times or not finish_times or len(start_times) != len(finish_times):
        return []

    n = len(start_times)

    # Create list of meetings with their original indices (1-based)
    meetings = []
    for i in range(n):
        meetings.append((start_times[i], finish_times[i], i + 1))  # (start, finish, meeting_number)

    # Sort meetings by finish time (greedy choice: earliest finish time first)
    meetings.sort(key=lambda x: x[1])

    selected_meetings = []
    last_finish_time = -1

    for start, finish, meeting_num in meetings:
        # If current meeting starts after the last selected meeting finishes
        if start >= last_finish_time:
            selected_meetings.append(meeting_num)
            last_finish_time = finish

    return selected_meetings


def print_meeting_schedule(start_times, finish_times):
    """
    Print the optimal meeting schedule in the required format.
    """
    selected = meeting_room_scheduler(start_times, finish_times)

    print("Selected meeting numbers:", " ".join(map(str, selected)))
    print("\nDetailed schedule:")

    for meeting_num in selected:
        idx = meeting_num - 1  # Convert to 0-based index
        start = start_times[idx]
        finish = finish_times[idx]
        print(f"Meeting {meeting_num}: [{start}, {finish}]")

    return selected


# Test with the sample input
def test_sample():
    """Test with the provided sample input."""
    print("=== Sample Test Case ===")
    start_times = [1, 3, 0, 5, 8, 5]
    finish_times = [2, 4, 6, 7, 9, 9]

    print(f"Input start times: {start_times}")
    print(f"Input finish times: {finish_times}")
    print()

    result = print_meeting_schedule(start_times, finish_times)

    # Verify the result matches expected output
    expected = [1, 2, 4, 5]
    assert result == expected, f"Expected {expected}, got {result}"
    print(f"\n✓ Test passed! Maximum meetings scheduled: {len(result)}")


# Additional test cases
def test_additional_cases():
    """Test additional edge cases and scenarios."""

    print("\n=== Additional Test Cases ===")

    # Test 1: No overlapping meetings
    print("\n--- Test 1: No overlapping meetings ---")
    start1 = [1, 3, 5, 7]
    finish1 = [2, 4, 6, 8]
    result1 = print_meeting_schedule(start1, finish1)
    assert len(result1) == 4, "All meetings should be selected"
    print("✓ All non-overlapping meetings selected")

    # Test 2: All meetings overlap
    print("\n--- Test 2: All meetings overlap ---")
    start2 = [1, 1, 1, 1]
    finish2 = [5, 5, 5, 5]
    result2 = print_meeting_schedule(start2, finish2)
    assert len(result2) == 1, "Only one meeting should be selected"
    print("✓ Only one meeting selected from overlapping set")

    # Test 3: Single meeting
    print("\n--- Test 3: Single meeting ---")
    start3 = [10]
    finish3 = [15]
    result3 = print_meeting_schedule(start3, finish3)
    assert result3 == [1], "Single meeting should be selected"
    print("✓ Single meeting handled correctly")

    # Test 4: Empty input
    print("\n--- Test 4: Empty input ---")
    result4 = meeting_room_scheduler([], [])
    assert result4 == [], "Empty input should return empty list"
    print("✓ Empty input handled correctly")


def visualize_algorithm():
    """Visualize how the greedy algorithm works step by step."""
    print("\n=== Algorithm Visualization ===")

    start_times = [1, 3, 0, 5, 8, 5]
    finish_times = [2, 4, 6, 7, 9, 9]

    print("Original meetings:")
    for i in range(len(start_times)):
        print(f"Meeting {i+1}: [{start_times[i]}, {finish_times[i]}]")

    # Create and sort meetings
    meetings = [(start_times[i], finish_times[i], i + 1) for i in range(len(start_times))]
    meetings.sort(key=lambda x: x[1])

    print("\nAfter sorting by finish time:")
    for start, finish, meeting_num in meetings:
        print(f"Meeting {meeting_num}: [{start}, {finish}]")

    print("\nGreedy selection process:")
    selected = []
    last_finish = -1

    for start, finish, meeting_num in meetings:
        if start >= last_finish:
            selected.append(meeting_num)
            last_finish = finish
            print(f"✓ Select Meeting {meeting_num}: [{start}, {finish}] (no conflict)")
        else:
            print(f"✗ Skip Meeting {meeting_num}: [{start}, {finish}] (conflicts with last finish time {last_finish})")

    print(f"\nFinal selection: {selected}")
    print(f"Maximum meetings scheduled: {len(selected)}")


if __name__ == "__main__":
    test_sample()
    test_additional_cases()
    visualize_algorithm()
    print("\n🎉 All tests passed!")